import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Button,
  Menu,
  MenuItem,
  Typography,
  IconButton,
  CircularProgress,
} from "@mui/material";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
// import { AccountContext } from "../login/AccountContext";
// import { useAuth } from "../auth/AuthProvider";
import useDrawerStore from "../../store/drawerStore";
import { AccountContext } from "../login/AccountContext";
import { useAuth } from "../auth/AuthProvider";
import useInfoStore from "../../store/UserInfoStore";
import { GetOrganizationThemesByAccountId } from "../../services/OrganizationService";
import { getThemesByAccountId } from "../../services/OrganizationService";


interface ThemeStyles {
  Typography: {
    fontFamily: string;
    fontSize: number;
    fontColor: string;
  };
  Canvas: {
    canvasPadding: number;
    canvasRadius: number;
    canvasBorderSize: number;
    canvasBorderColor: string;
    canvasBgColor: string;
  };
  Button: {
    shape: "square" | "curved" | "round";
    primaryBtnBg: string;
    primaryBtnColor: string;
    secondaryBtnBg: string;
    secondaryBtnColor: string;
  };
}

interface OrganizationTheme {
  Id: string;
  organizationId: string;
  accountId: string;
  userId: string;
  ThemeName: string;
  themeDescription: string;
  level: string;
  IsActive: boolean;
  ThemeStyles?: ThemeStyles; // made optional for safety
}

const DETACHED_THEME: OrganizationTheme = {
    Id: "detached-theme",
    organizationId: "",
    accountId: "",
    userId: "",
    ThemeName: "Theme Detached",
    themeDescription: "Unsaved changes",
    level: "Interaction",
    IsActive: false,
    ThemeStyles: undefined,
  };
  

const ThemeDropdown = () => {
  const [themes, setThemes] = useState<OrganizationTheme[]>([]);
  const [selectedThemes, setSelectedThemes] = useState<OrganizationTheme | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [loading, setLoading] = useState(false);
  const { setSelectedTheme, isUnSavedChanges, setIsThemeChanges , isThemeChanges } = useDrawerStore();
  const { accountId } = useContext(AccountContext);
  const { userDetails } = useAuth();
  const orgDetails = useInfoStore((state) => state.orgDetails || "{}");
	const organizationId = orgDetails.OrganizationId;

  useEffect(() => {
    const fetchThemes = async () => {
      setLoading(true);
      try {
        const accId = accountId;
        const orgId = organizationId;
        const level = "Interaction";
  
        const data: OrganizationTheme[] = await GetOrganizationThemesByAccountId(accId, orgId, level);
  
        // ✅ Separate active and inactive themes
        const activeThemes = data.filter(t => t.IsActive);
        const inactiveThemes = data.filter(t => !t.IsActive);
  
        // ✅ Merge active first, then inactive
        let sortedThemes = [...activeThemes, ...inactiveThemes];
        if (isThemeChanges) {
            sortedThemes = [DETACHED_THEME, ...sortedThemes];
            setSelectedThemes(DETACHED_THEME);
            setSelectedTheme(DETACHED_THEME);
            
          } else {
            const selected = activeThemes.find(t => t.ThemeStyles?.Button) || sortedThemes[0];
            setSelectedThemes(selected);
            setSelectedTheme(selected);
            setIsThemeChanges(false);


          }
        setThemes(sortedThemes); // Update list for dropdown

        // ✅ Set first active theme, or fallback to first
        
  
      } catch (err) {
        console.error("❌ Theme fetch failed:", err);
      } finally {
        setLoading(false);
      }
    };
  
    fetchThemes();
  }, [isThemeChanges]);
  

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => setAnchorEl(null);

  const handleSelectTheme = (theme: OrganizationTheme) => {

    if (theme.Id !== "detached-theme") {
        setThemes(prev => prev.filter(t => t.Id !== "detached-theme"));
      }
    setSelectedThemes(theme);
    setSelectedTheme(theme);
    handleClose();

    // Apply theme styles
    if (theme.ThemeStyles) {
      console.log('🎨 Applying Theme for:', theme.ThemeName);

      // Apply theme styles as CSS variables
      Object.entries(theme.ThemeStyles).forEach(([key, value]) => {
        if (value) {
          const cssVarName = `--Theme-${key}`;
          document.documentElement.style.setProperty(cssVarName, value as string);
        }
      });

      // Generate and apply opacity variants for primaryColor, secondaryColor, accentColor
      const targetColors = ['primaryColor', 'secondaryColor', 'accentColor'];
      const opacities = [10, 20, 30, 40, 50];
      const themeStyles = theme.ThemeStyles as any;

      targetColors.forEach(colorKey => {
        const colorValue = themeStyles[colorKey];
        if (colorValue && typeof colorValue === 'string') {
          // Set the base color
          document.documentElement.style.setProperty(`--${colorKey}`, colorValue);

          // Get RGB values for color mixing
          let baseR = 0, baseG = 0, baseB = 0;

          // Convert color to RGB values
          if (colorValue.startsWith('#')) {
            const hex = colorValue.replace('#', '');
            baseR = parseInt(hex.substring(0, 2), 16);
            baseG = parseInt(hex.substring(2, 4), 16);
            baseB = parseInt(hex.substring(4, 6), 16);
          } else if (colorValue.startsWith('rgb')) {
            const rgbMatch = colorValue.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/);
            if (rgbMatch) {
              baseR = parseInt(rgbMatch[1]);
              baseG = parseInt(rgbMatch[2]);
              baseB = parseInt(rgbMatch[3]);
            }
          }

          // Generate opacity variants
          opacities.forEach(opacity => {
            const alpha = opacity / 100;
            const rgbaValue = `rgba(${baseR}, ${baseG}, ${baseB}, ${alpha})`;
            const varName = `--${colorKey}-${opacity}`;
            document.documentElement.style.setProperty(varName, rgbaValue);
          });

          // Generate white-mixed variants (lighter tints)
          opacities.forEach(whitePercent => {
            const whiteMix = whitePercent / 100;
            // Mix with white: newColor = originalColor * (1 - whiteMix) + white * whiteMix
            const mixedR = Math.round(baseR * (1 - whiteMix) + 255 * whiteMix);
            const mixedG = Math.round(baseG * (1 - whiteMix) + 255 * whiteMix);
            const mixedB = Math.round(baseB * (1 - whiteMix) + 255 * whiteMix);

            const whiteVariantValue = `rgb(${mixedR}, ${mixedG}, ${mixedB})`;
            const whiteVarName = `--${colorKey}-white-${whitePercent}`;
            document.documentElement.style.setProperty(whiteVarName, whiteVariantValue);
          });
        }
      });
    }

    // Optional: Apply globally via context
    // dispatch({ type: "SET_THEME", payload: theme.ThemeStyles });
  };

  const getBtnColor = (theme: OrganizationTheme, key: "primaryBtnBg" | "secondaryBtnBg") =>
    theme?.ThemeStyles?.Button?.[key] || "#ccc";



  return (
    <Box display="flex" alignItems="center" gap={1}>
      <Button
        variant="outlined"
        onClick={handleClick}
        endIcon={<ArrowDropDownIcon />}
        startIcon={
          selectedThemes?.ThemeStyles?.Button?.primaryBtnBg ? (
            <Box
              sx={{
                width: 12,
                height: 12,
                borderRadius: "50%",
                backgroundColor: selectedThemes.ThemeStyles.Button.primaryBtnBg,
              }}
            />
          ) : null
        }
        sx={{ textTransform: "none", borderRadius: 3 }}
        disabled={loading}
      >
        {loading ? <CircularProgress size={18} /> : selectedThemes?.ThemeName || "Select Theme"}
      </Button>

      

      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        {themes.map((theme) => (
          <MenuItem
            key={theme.Id}
            onClick={() => 
                {handleSelectTheme(theme)
                    setIsThemeChanges(false);

                }
            }
            selected={selectedThemes?.Id === theme.Id}
          >
            <Box display="flex" alignItems="center" gap={1}>
              <Typography>{theme.ThemeName}</Typography>
              <Box display="flex" gap={0.5}>
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: "50%",
                    backgroundColor: getBtnColor(theme, "primaryBtnBg"),
                  }}
                />
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: "50%",
                    backgroundColor: getBtnColor(theme, "secondaryBtnBg"),
                  }}
                />
              </Box>
            </Box>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default ThemeDropdown;
