[{"E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "5", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "6", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "7", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "10", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "11", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "12", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "13", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "14", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "15", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "16", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "17", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "18", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "19", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "20", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "21", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "22", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "32", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "33", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "37", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "38", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "39", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "40", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "41", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "42", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "43", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "44", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "45", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "46", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "47", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "48", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "49", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "50", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "51", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "52", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "53", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "54", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "55", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "56", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "57", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "58", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "59", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "60", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "61", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "62", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "63", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "64", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "65", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "66", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "67", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "68", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "69", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "70", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "71", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "72", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "73", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "74", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "75", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "76", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "77", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "78", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "79", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "80", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "81", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "82", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "83", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "84", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "85", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "86", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "87", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "88", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts": "89", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "90", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "91", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "92", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "93", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx": "94", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx": "95", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "96", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\ThemeDropdown.tsx": "97"}, {"size": 604, "mtime": 1754648094631, "results": "98", "hashOfConfig": "99"}, {"size": 440, "mtime": 1748929949904, "results": "100", "hashOfConfig": "99"}, {"size": 2367, "mtime": 1754627697194, "results": "101", "hashOfConfig": "99"}, {"size": 3344, "mtime": 1753158986544, "results": "102", "hashOfConfig": "99"}, {"size": 253916, "mtime": 1754652068311, "results": "103", "hashOfConfig": "99"}, {"size": 6890, "mtime": 1753072098544, "results": "104", "hashOfConfig": "99"}, {"size": 3102, "mtime": 1753781192983, "results": "105", "hashOfConfig": "99"}, {"size": 3112, "mtime": 1753072087888, "results": "106", "hashOfConfig": "99"}, {"size": 402978, "mtime": 1754649980201, "results": "107", "hashOfConfig": "99"}, {"size": 3927, "mtime": 1748930023076, "results": "108", "hashOfConfig": "99"}, {"size": 6144, "mtime": 1749531263982, "results": "109", "hashOfConfig": "99"}, {"size": 42378, "mtime": 1754649973939, "results": "110", "hashOfConfig": "99"}, {"size": 5070, "mtime": 1754649672541, "results": "111", "hashOfConfig": "99"}, {"size": 3746, "mtime": 1754649672545, "results": "112", "hashOfConfig": "99"}, {"size": 16369, "mtime": 1754652068145, "results": "113", "hashOfConfig": "99"}, {"size": 13422, "mtime": 1754651504206, "results": "114", "hashOfConfig": "99"}, {"size": 56751, "mtime": 1753781192998, "results": "115", "hashOfConfig": "99"}, {"size": 1898, "mtime": 1748930023076, "results": "116", "hashOfConfig": "99"}, {"size": 1956, "mtime": 1754471009017, "results": "117", "hashOfConfig": "99"}, {"size": 9087, "mtime": 1753158986542, "results": "118", "hashOfConfig": "99"}, {"size": 298702, "mtime": 1754647416331, "results": "119", "hashOfConfig": "99"}, {"size": 193, "mtime": 1748929949654, "results": "120", "hashOfConfig": "99"}, {"size": 9097, "mtime": 1753158986506, "results": "121", "hashOfConfig": "99"}, {"size": 30700, "mtime": 1753072098544, "results": "122", "hashOfConfig": "99"}, {"size": 3000, "mtime": 1754627697225, "results": "123", "hashOfConfig": "99"}, {"size": 2606, "mtime": 1754648339852, "results": "124", "hashOfConfig": "99"}, {"size": 33249, "mtime": 1754649672493, "results": "125", "hashOfConfig": "99"}, {"size": 23356, "mtime": 1754627697225, "results": "126", "hashOfConfig": "99"}, {"size": 13556, "mtime": 1754627697194, "results": "127", "hashOfConfig": "99"}, {"size": 27068, "mtime": 1754627697194, "results": "128", "hashOfConfig": "99"}, {"size": 49705, "mtime": 1754627697194, "results": "129", "hashOfConfig": "99"}, {"size": 7616, "mtime": 1754649975893, "results": "130", "hashOfConfig": "99"}, {"size": 32556, "mtime": 1754627697210, "results": "131", "hashOfConfig": "99"}, {"size": 11315, "mtime": 1754649670499, "results": "132", "hashOfConfig": "99"}, {"size": 24210, "mtime": 1754649670729, "results": "133", "hashOfConfig": "99"}, {"size": 4880, "mtime": 1750229130169, "results": "134", "hashOfConfig": "99"}, {"size": 10652, "mtime": 1754627697256, "results": "135", "hashOfConfig": "99"}, {"size": 1297, "mtime": 1754627697256, "results": "136", "hashOfConfig": "99"}, {"size": 1248, "mtime": 1748929949920, "results": "137", "hashOfConfig": "99"}, {"size": 14238, "mtime": 1748930023076, "results": "138", "hashOfConfig": "99"}, {"size": 2997, "mtime": 1753072087872, "results": "139", "hashOfConfig": "99"}, {"size": 3279, "mtime": 1754648339974, "results": "140", "hashOfConfig": "99"}, {"size": 2717, "mtime": 1754627697225, "results": "141", "hashOfConfig": "99"}, {"size": 2052, "mtime": 1753781192967, "results": "142", "hashOfConfig": "99"}, {"size": 20267, "mtime": 1754648094616, "results": "143", "hashOfConfig": "99"}, {"size": 743, "mtime": 1748929949654, "results": "144", "hashOfConfig": "99"}, {"size": 2608, "mtime": 1748930023061, "results": "145", "hashOfConfig": "99"}, {"size": 31149, "mtime": 1754648339976, "results": "146", "hashOfConfig": "99"}, {"size": 7772, "mtime": 1753072087872, "results": "147", "hashOfConfig": "99"}, {"size": 16100, "mtime": 1754649974351, "results": "148", "hashOfConfig": "99"}, {"size": 29119, "mtime": 1753158986510, "results": "149", "hashOfConfig": "99"}, {"size": 6262, "mtime": 1754649973992, "results": "150", "hashOfConfig": "99"}, {"size": 2034, "mtime": 1753072098528, "results": "151", "hashOfConfig": "99"}, {"size": 29744, "mtime": 1753158986497, "results": "152", "hashOfConfig": "99"}, {"size": 1962, "mtime": 1748929949654, "results": "153", "hashOfConfig": "99"}, {"size": 27274, "mtime": 1754649975889, "results": "154", "hashOfConfig": "99"}, {"size": 2423, "mtime": 1753781192967, "results": "155", "hashOfConfig": "99"}, {"size": 702, "mtime": 1753072087841, "results": "156", "hashOfConfig": "99"}, {"size": 13894, "mtime": 1754649672481, "results": "157", "hashOfConfig": "99"}, {"size": 19888, "mtime": 1754627697225, "results": "158", "hashOfConfig": "99"}, {"size": 6625, "mtime": 1754648094590, "results": "159", "hashOfConfig": "99"}, {"size": 20406, "mtime": 1754649670732, "results": "160", "hashOfConfig": "99"}, {"size": 3236, "mtime": 1754648094578, "results": "161", "hashOfConfig": "99"}, {"size": 2848, "mtime": 1748929949811, "results": "162", "hashOfConfig": "99"}, {"size": 16085, "mtime": 1754649975818, "results": "163", "hashOfConfig": "99"}, {"size": 15261, "mtime": 1754648094540, "results": "164", "hashOfConfig": "99"}, {"size": 11263, "mtime": 1754649670637, "results": "165", "hashOfConfig": "99"}, {"size": 18460, "mtime": 1754649973701, "results": "166", "hashOfConfig": "99"}, {"size": 8476, "mtime": 1753072087856, "results": "167", "hashOfConfig": "99"}, {"size": 15571, "mtime": 1754627697241, "results": "168", "hashOfConfig": "99"}, {"size": 16126, "mtime": 1754627697256, "results": "169", "hashOfConfig": "99"}, {"size": 32721, "mtime": 1754649975292, "results": "170", "hashOfConfig": "99"}, {"size": 61442, "mtime": 1754049132308, "results": "171", "hashOfConfig": "99"}, {"size": 26748, "mtime": 1754627697210, "results": "172", "hashOfConfig": "99"}, {"size": 5258, "mtime": 1753072087872, "results": "173", "hashOfConfig": "99"}, {"size": 883, "mtime": 1748929949889, "results": "174", "hashOfConfig": "99"}, {"size": 2196, "mtime": 1754627697225, "results": "175", "hashOfConfig": "99"}, {"size": 7943, "mtime": 1748930023061, "results": "176", "hashOfConfig": "99"}, {"size": 1092, "mtime": 1754627697256, "results": "177", "hashOfConfig": "99"}, {"size": 5504, "mtime": 1753072087841, "results": "178", "hashOfConfig": "99"}, {"size": 33129, "mtime": 1754649672522, "results": "179", "hashOfConfig": "99"}, {"size": 37240, "mtime": 1754649672545, "results": "180", "hashOfConfig": "99"}, {"size": 2965, "mtime": 1754649973974, "results": "181", "hashOfConfig": "99"}, {"size": 2669, "mtime": 1748929949748, "results": "182", "hashOfConfig": "99"}, {"size": 17475, "mtime": 1754649975820, "results": "183", "hashOfConfig": "99"}, {"size": 27589, "mtime": 1754627697210, "results": "184", "hashOfConfig": "99"}, {"size": 10852, "mtime": 1754648339860, "results": "185", "hashOfConfig": "99"}, {"size": 16338, "mtime": 1754649974243, "results": "186", "hashOfConfig": "99"}, {"size": 677, "mtime": 1753072098575, "results": "187", "hashOfConfig": "99"}, {"size": 6886, "mtime": 1753158986540, "results": "188", "hashOfConfig": "99"}, {"size": 7158, "mtime": 1753158986535, "results": "189", "hashOfConfig": "99"}, {"size": 10160, "mtime": 1754627697210, "results": "190", "hashOfConfig": "99"}, {"size": 1211, "mtime": 1753158986538, "results": "191", "hashOfConfig": "99"}, {"size": 3836, "mtime": 1753781192967, "results": "192", "hashOfConfig": "99"}, {"size": 3021, "mtime": 1753781192967, "results": "193", "hashOfConfig": "99"}, {"size": 24844, "mtime": 1754627697241, "results": "194", "hashOfConfig": "99"}, {"size": 9479, "mtime": 1754652068628, "results": "195", "hashOfConfig": "99"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c51j82", {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 225, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 50, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 64, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 34, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["487", "488"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["489"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["715", "716", "717", "718", "719", "720"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["721", "722"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["747"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["748"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["791"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["849", "850", "851"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["852", "853", "854", "855", "856", "857", "858", "859"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["887", "888", "889", "890"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1152", "1153", "1154", "1155", "1156", "1157"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1158", "1159", "1160", "1161"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1162", "1163"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1164", "1165", "1166"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1167", "1168"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1169"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1170", "1171", "1172"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1233", "1234", "1235", "1236", "1237"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1363", "1364", "1365", "1366", "1367"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1368", "1369", "1370", "1371", "1372", "1373"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1394", "1395", "1396"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1397", "1398"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1677", "1678"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1843", "1844"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916", "1917"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts", ["1918"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", ["1919", "1920", "1921"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1922", "1923"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1924"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx", ["1925", "1926", "1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939", "1940", "1941", "1942", "1943", "1944"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1945", "1946", "1947"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\ThemeDropdown.tsx", ["1948", "1949", "1950", "1951", "1952", "1953"], [], {"ruleId": "1954", "severity": 1, "message": "1955", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "1958", "line": 9, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "1959", "line": 1, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "1960", "line": 1, "column": 58, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 65}, {"ruleId": "1954", "severity": 1, "message": "1961", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "1962", "line": 6, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "1963", "line": 7, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "1964", "line": 15, "column": 28, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 40}, {"ruleId": "1954", "severity": 1, "message": "1965", "line": 20, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "1966", "line": 25, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "1967", "line": 26, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "1968", "line": 27, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 27, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "1969", "line": 28, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 28, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "1970", "line": 29, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 29, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "1971", "line": 30, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 30, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "1972", "line": 31, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "1973", "line": 32, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 32, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "1974", "line": 33, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 33, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "1975", "line": 34, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 34, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "1976", "line": 35, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 35, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "1977", "line": 36, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 36, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "1978", "line": 37, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 37, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "1979", "line": 38, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 38, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "1980", "line": 40, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 40, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "1981", "line": 41, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 41, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "1982", "line": 42, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 42, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "1983", "line": 43, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 43, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "1984", "line": 47, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 47, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "1985", "line": 53, "column": 20, "nodeType": "1956", "messageId": "1957", "endLine": 53, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 63, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 63, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "1987", "line": 64, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 64, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "1988", "line": 71, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 71, "endColumn": 6}, {"ruleId": "1954", "severity": 1, "message": "1989", "line": 72, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 72, "endColumn": 6}, {"ruleId": "1954", "severity": 1, "message": "1990", "line": 79, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 79, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "1991", "line": 81, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 81, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "1992", "line": 82, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 82, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "1993", "line": 82, "column": 30, "nodeType": "1956", "messageId": "1957", "endLine": 82, "endColumn": 39}, {"ruleId": "1954", "severity": 1, "message": "1994", "line": 85, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 85, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "1995", "line": 86, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 86, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "1996", "line": 87, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 87, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "1997", "line": 91, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 91, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "1998", "line": 93, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 93, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "1999", "line": 99, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 99, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2000", "line": 106, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 106, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2001", "line": 109, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 109, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2002", "line": 110, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 110, "endColumn": 37}, {"ruleId": "1954", "severity": 1, "message": "2003", "line": 115, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 115, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2004", "line": 115, "column": 21, "nodeType": "1956", "messageId": "1957", "endLine": 115, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2005", "line": 118, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 118, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2006", "line": 125, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 125, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2007", "line": 133, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 133, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2008", "line": 139, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 139, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2009", "line": 202, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 202, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2010", "line": 219, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 219, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2011", "line": 227, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 227, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2012", "line": 383, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 383, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2013", "line": 419, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 419, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2014", "line": 421, "column": 6, "nodeType": "1956", "messageId": "1957", "endLine": 421, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2015", "line": 424, "column": 6, "nodeType": "1956", "messageId": "1957", "endLine": 424, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2016", "line": 440, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 440, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2017", "line": 441, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 441, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2018", "line": 443, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 443, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2019", "line": 446, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 446, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2020", "line": 450, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 450, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2021", "line": 451, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 451, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2022", "line": 462, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 462, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2023", "line": 463, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 463, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2024", "line": 464, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 464, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2025", "line": 466, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 466, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2026", "line": 466, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 466, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2027", "line": 471, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 471, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2028", "line": 471, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 471, "endColumn": 38}, {"ruleId": "1954", "severity": 1, "message": "2029", "line": 473, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 473, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2030", "line": 473, "column": 19, "nodeType": "1956", "messageId": "1957", "endLine": 473, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2031", "line": 476, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 476, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2032", "line": 476, "column": 24, "nodeType": "1956", "messageId": "1957", "endLine": 476, "endColumn": 40}, {"ruleId": "1954", "severity": 1, "message": "2033", "line": 477, "column": 19, "nodeType": "1956", "messageId": "1957", "endLine": 477, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2034", "line": 482, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 482, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2035", "line": 482, "column": 29, "nodeType": "1956", "messageId": "1957", "endLine": 482, "endColumn": 50}, {"ruleId": "1954", "severity": 1, "message": "2036", "line": 489, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 489, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2037", "line": 489, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 489, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2038", "line": 491, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 491, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2039", "line": 491, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 491, "endColumn": 41}, {"ruleId": "1954", "severity": 1, "message": "2040", "line": 493, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 493, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2041", "line": 493, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 493, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2042", "line": 507, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 507, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2043", "line": 508, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 508, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2044", "line": 508, "column": 33, "nodeType": "1956", "messageId": "1957", "endLine": 508, "endColumn": 58}, {"ruleId": "1954", "severity": 1, "message": "2045", "line": 511, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 511, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2046", "line": 511, "column": 19, "nodeType": "1956", "messageId": "1957", "endLine": 511, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2047", "line": 512, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 512, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2048", "line": 512, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 512, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2049", "line": 513, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 513, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2050", "line": 513, "column": 21, "nodeType": "1956", "messageId": "1957", "endLine": 513, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2051", "line": 522, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 522, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2052", "line": 523, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 523, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2053", "line": 529, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 529, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2054", "line": 533, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 533, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2055", "line": 533, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 533, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2056", "line": 536, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 536, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2057", "line": 536, "column": 20, "nodeType": "1956", "messageId": "1957", "endLine": 536, "endColumn": 32}, {"ruleId": "2058", "severity": 1, "message": "2059", "line": 576, "column": 5, "nodeType": "2060", "endLine": 576, "endColumn": 27, "suggestions": "2061"}, {"ruleId": "1954", "severity": 1, "message": "2062", "line": 586, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 586, "endColumn": 6}, {"ruleId": "1954", "severity": 1, "message": "2063", "line": 587, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 587, "endColumn": 7}, {"ruleId": "1954", "severity": 1, "message": "2064", "line": 590, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 590, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2065", "line": 591, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 591, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2066", "line": 596, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 596, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2067", "line": 597, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 597, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2068", "line": 632, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 632, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2069", "line": 633, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 633, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2070", "line": 634, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 634, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2071", "line": 642, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 642, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2072", "line": 644, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 644, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2073", "line": 645, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 645, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2074", "line": 646, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 646, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2075", "line": 647, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 647, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2076", "line": 652, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 652, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2077", "line": 654, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 654, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2078", "line": 666, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 666, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2079", "line": 667, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 667, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2080", "line": 670, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 670, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2081", "line": 674, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 674, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2082", "line": 676, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 676, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2083", "line": 677, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 677, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2084", "line": 679, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 679, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2085", "line": 686, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 686, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2086", "line": 687, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 687, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2087", "line": 692, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 692, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2088", "line": 693, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 693, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2089", "line": 694, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 694, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2090", "line": 704, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 704, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2091", "line": 708, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 708, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2092", "line": 712, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 712, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2093", "line": 714, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 714, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2094", "line": 716, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 716, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2095", "line": 717, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 717, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2096", "line": 722, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 722, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2097", "line": 723, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 723, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2098", "line": 741, "column": 18, "nodeType": "1956", "messageId": "1957", "endLine": 741, "endColumn": 37}, {"ruleId": "1954", "severity": 1, "message": "2099", "line": 742, "column": 18, "nodeType": "1956", "messageId": "1957", "endLine": 742, "endColumn": 37}, {"ruleId": "1954", "severity": 1, "message": "2100", "line": 746, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 746, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2101", "line": 758, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 758, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2102", "line": 795, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 795, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2103", "line": 806, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 806, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2104", "line": 811, "column": 25, "nodeType": "1956", "messageId": "1957", "endLine": 811, "endColumn": 42}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 816, "column": 22, "nodeType": "2107", "messageId": "2108", "endLine": 816, "endColumn": 24}, {"ruleId": "2058", "severity": 1, "message": "2109", "line": 902, "column": 5, "nodeType": "2060", "endLine": 902, "endColumn": 46, "suggestions": "2110"}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 902, "column": 6, "nodeType": "2112", "endLine": 902, "endColumn": 29}, {"ruleId": "2058", "severity": 1, "message": "2113", "line": 920, "column": 5, "nodeType": "2060", "endLine": 920, "endColumn": 18, "suggestions": "2114"}, {"ruleId": "1954", "severity": 1, "message": "2115", "line": 922, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 922, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2116", "line": 923, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 923, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2117", "line": 944, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 944, "endColumn": 24}, {"ruleId": "2058", "severity": 1, "message": "2118", "line": 1005, "column": 5, "nodeType": "2060", "endLine": 1013, "endColumn": 3, "suggestions": "2119"}, {"ruleId": "2058", "severity": 1, "message": "2120", "line": 1041, "column": 5, "nodeType": "2060", "endLine": 1064, "endColumn": 3, "suggestions": "2121"}, {"ruleId": "2058", "severity": 1, "message": "2122", "line": 1076, "column": 5, "nodeType": "2060", "endLine": 1076, "endColumn": 19, "suggestions": "2123"}, {"ruleId": "2058", "severity": 1, "message": "2124", "line": 1187, "column": 5, "nodeType": "2060", "endLine": 1187, "endColumn": 39, "suggestions": "2125"}, {"ruleId": "1954", "severity": 1, "message": "2126", "line": 1306, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 1306, "endColumn": 24}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 1397, "column": 25, "nodeType": "2107", "messageId": "2108", "endLine": 1397, "endColumn": 27}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 1404, "column": 25, "nodeType": "2107", "messageId": "2108", "endLine": 1404, "endColumn": 27}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 1404, "column": 53, "nodeType": "2107", "messageId": "2108", "endLine": 1404, "endColumn": 55}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 1407, "column": 26, "nodeType": "2107", "messageId": "2108", "endLine": 1407, "endColumn": 28}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 1407, "column": 58, "nodeType": "2107", "messageId": "2108", "endLine": 1407, "endColumn": 60}, {"ruleId": "1954", "severity": 1, "message": "2128", "line": 1538, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 1538, "endColumn": 33}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 1615, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 1615, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2129", "line": 1762, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 1762, "endColumn": 30}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 2024, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 2024, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 2196, "column": 25, "nodeType": "2107", "messageId": "2108", "endLine": 2196, "endColumn": 27}, {"ruleId": "2058", "severity": 1, "message": "2130", "line": 2235, "column": 5, "nodeType": "2060", "endLine": 2235, "endColumn": 69, "suggestions": "2131"}, {"ruleId": "1954", "severity": 1, "message": "2132", "line": 2292, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 2292, "endColumn": 36}, {"ruleId": "1954", "severity": 1, "message": "2133", "line": 2303, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 2303, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2134", "line": 2303, "column": 29, "nodeType": "1956", "messageId": "1957", "endLine": 2303, "endColumn": 48}, {"ruleId": "1954", "severity": 1, "message": "2135", "line": 2697, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 2697, "endColumn": 27}, {"ruleId": "2058", "severity": 1, "message": "2136", "line": 2732, "column": 5, "nodeType": "2060", "endLine": 2732, "endColumn": 38, "suggestions": "2137"}, {"ruleId": "1954", "severity": 1, "message": "2138", "line": 2749, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 2749, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2139", "line": 2783, "column": 6, "nodeType": "1956", "messageId": "1957", "endLine": 2783, "endColumn": 18}, {"ruleId": "2058", "severity": 1, "message": "2140", "line": 3169, "column": 4, "nodeType": "2060", "endLine": 3169, "endColumn": 18, "suggestions": "2141"}, {"ruleId": "1954", "severity": 1, "message": "2142", "line": 3517, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3517, "endColumn": 33}, {"ruleId": "2058", "severity": 1, "message": "2143", "line": 3591, "column": 16, "nodeType": "2112", "endLine": 3591, "endColumn": 37}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 3592, "column": 56, "nodeType": "2107", "messageId": "2108", "endLine": 3592, "endColumn": 58}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 3596, "column": 49, "nodeType": "2107", "messageId": "2108", "endLine": 3596, "endColumn": 51}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 3600, "column": 50, "nodeType": "2107", "messageId": "2108", "endLine": 3600, "endColumn": 52}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 3606, "column": 51, "nodeType": "2107", "messageId": "2108", "endLine": 3606, "endColumn": 53}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 3613, "column": 51, "nodeType": "2107", "messageId": "2108", "endLine": 3613, "endColumn": 53}, {"ruleId": "1954", "severity": 1, "message": "2144", "line": 3840, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3840, "endColumn": 23}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 3848, "column": 30, "nodeType": "2107", "messageId": "2108", "endLine": 3848, "endColumn": 32}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 3861, "column": 39, "nodeType": "2107", "messageId": "2108", "endLine": 3861, "endColumn": 41}, {"ruleId": "2058", "severity": 1, "message": "2145", "line": 3875, "column": 5, "nodeType": "2060", "endLine": 3875, "endColumn": 33, "suggestions": "2146"}, {"ruleId": "1954", "severity": 1, "message": "2147", "line": 3879, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 3879, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2148", "line": 3879, "column": 30, "nodeType": "1956", "messageId": "1957", "endLine": 3879, "endColumn": 52}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 3976, "column": 55, "nodeType": "2107", "messageId": "2108", "endLine": 3976, "endColumn": 57}, {"ruleId": "1954", "severity": 1, "message": "2149", "line": 3996, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3996, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2150", "line": 3998, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 3998, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2151", "line": 4002, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4002, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2152", "line": 4021, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 4021, "endColumn": 26}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 4045, "column": 66, "nodeType": "2107", "messageId": "2108", "endLine": 4045, "endColumn": 68}, {"ruleId": "2058", "severity": 1, "message": "2153", "line": 4052, "column": 5, "nodeType": "2060", "endLine": 4059, "endColumn": 3, "suggestions": "2154"}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 4293, "column": 17, "nodeType": "2107", "messageId": "2108", "endLine": 4293, "endColumn": 19}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 4547, "column": 21, "nodeType": "2107", "messageId": "2108", "endLine": 4547, "endColumn": 23}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 4555, "column": 21, "nodeType": "2107", "messageId": "2108", "endLine": 4555, "endColumn": 23}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 4568, "column": 15, "nodeType": "2107", "messageId": "2108", "endLine": 4568, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2155", "line": 4865, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 4865, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2156", "line": 4876, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 4876, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2157", "line": 4877, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 4877, "endColumn": 20}, {"ruleId": "2058", "severity": 1, "message": "2158", "line": 4883, "column": 5, "nodeType": "2060", "endLine": 4883, "endColumn": 62, "suggestions": "2159"}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 4883, "column": 6, "nodeType": "2160", "endLine": 4883, "endColumn": 48}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 4906, "column": 25, "nodeType": "2107", "messageId": "2108", "endLine": 4906, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2161", "line": 4910, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4910, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2162", "line": 4933, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 4933, "endColumn": 23}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 5008, "column": 25, "nodeType": "2107", "messageId": "2108", "endLine": 5008, "endColumn": 27}, {"ruleId": "2058", "severity": 1, "message": "2163", "line": 5041, "column": 5, "nodeType": "2060", "endLine": 5041, "endColumn": 22, "suggestions": "2164"}, {"ruleId": "1954", "severity": 1, "message": "2165", "line": 5043, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 5043, "endColumn": 18}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 5045, "column": 40, "nodeType": "2107", "messageId": "2108", "endLine": 5045, "endColumn": 42}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 5110, "column": 69, "nodeType": "2107", "messageId": "2108", "endLine": 5110, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2166", "line": 5160, "column": 12, "nodeType": "1956", "messageId": "1957", "endLine": 5160, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2167", "line": 5161, "column": 12, "nodeType": "1956", "messageId": "1957", "endLine": 5161, "endColumn": 22}, {"ruleId": "2058", "severity": 1, "message": "2168", "line": 5191, "column": 5, "nodeType": "2060", "endLine": 5191, "endColumn": 38, "suggestions": "2169"}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 5194, "column": 40, "nodeType": "2107", "messageId": "2108", "endLine": 5194, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2166", "line": 5200, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5200, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2167", "line": 5201, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5201, "endColumn": 20}, {"ruleId": "2058", "severity": 1, "message": "2170", "line": 5207, "column": 5, "nodeType": "2060", "endLine": 5207, "endColumn": 106, "suggestions": "2171"}, {"ruleId": "2058", "severity": 1, "message": "2172", "line": 5271, "column": 5, "nodeType": "2060", "endLine": 5271, "endColumn": 16, "suggestions": "2173"}, {"ruleId": "1954", "severity": 1, "message": "2174", "line": 5441, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 5441, "endColumn": 21}, {"ruleId": "2058", "severity": 1, "message": "2175", "line": 5476, "column": 7, "nodeType": "2060", "endLine": 5476, "endColumn": 29, "suggestions": "2176"}, {"ruleId": "2058", "severity": 1, "message": "2177", "line": 5528, "column": 5, "nodeType": "2060", "endLine": 5528, "endColumn": 17, "suggestions": "2178"}, {"ruleId": "2058", "severity": 1, "message": "2179", "line": 5544, "column": 5, "nodeType": "2060", "endLine": 5544, "endColumn": 78, "suggestions": "2180"}, {"ruleId": "1954", "severity": 1, "message": "2181", "line": 5547, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 5547, "endColumn": 29}, {"ruleId": "2182", "severity": 1, "message": "2183", "line": 6211, "column": 80, "nodeType": "2184", "messageId": "2185", "endLine": 6211, "endColumn": 81, "suggestions": "2186"}, {"ruleId": "1954", "severity": 1, "message": "2187", "line": 6426, "column": 25, "nodeType": "1956", "messageId": "1957", "endLine": 6426, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2188", "line": 2, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2189", "line": 7, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2190", "line": 7, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2191", "line": 98, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 98, "endColumn": 25}, {"ruleId": "2058", "severity": 1, "message": "2192", "line": 103, "column": 6, "nodeType": "2060", "endLine": 103, "endColumn": 8, "suggestions": "2193"}, {"ruleId": "1954", "severity": 1, "message": "2194", "line": 148, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 148, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2195", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2196", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2197", "line": 3, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2198", "line": 8, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2199", "line": 9, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2200", "line": 13, "column": 24, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 46}, {"ruleId": "1954", "severity": 1, "message": "2201", "line": 1736, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 1736, "endColumn": 41}, {"ruleId": "2202", "severity": 1, "message": "2203", "line": 2569, "column": 5, "nodeType": "2204", "messageId": "2108", "endLine": 2569, "endColumn": 17}, {"ruleId": "2202", "severity": 1, "message": "2205", "line": 2570, "column": 5, "nodeType": "2204", "messageId": "2108", "endLine": 2570, "endColumn": 20}, {"ruleId": "2202", "severity": 1, "message": "2206", "line": 2901, "column": 5, "nodeType": "2204", "messageId": "2108", "endLine": 2901, "endColumn": 24}, {"ruleId": "2207", "severity": 1, "message": "2208", "line": 2907, "column": 31, "nodeType": "2112", "messageId": "2209", "endLine": 2907, "endColumn": 51}, {"ruleId": "2202", "severity": 1, "message": "2210", "line": 3078, "column": 5, "nodeType": "2204", "messageId": "2108", "endLine": 3078, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2211", "line": 3448, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 3448, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2212", "line": 3841, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 3841, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2212", "line": 4040, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 4040, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2213", "line": 5318, "column": 14, "nodeType": "1956", "messageId": "1957", "endLine": 5318, "endColumn": 35}, {"ruleId": "2202", "severity": 1, "message": "2214", "line": 5484, "column": 5, "nodeType": "2204", "messageId": "2108", "endLine": 5484, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2215", "line": 5547, "column": 14, "nodeType": "1956", "messageId": "1957", "endLine": 5547, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2216", "line": 6700, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 6700, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2216", "line": 6726, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 6726, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2216", "line": 6732, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 6732, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2216", "line": 6747, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 6747, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2215", "line": 7524, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 7524, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2215", "line": 7768, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 7768, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2215", "line": 7939, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 7939, "endColumn": 16}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 8604, "column": 66, "nodeType": "2107", "messageId": "2108", "endLine": 8604, "endColumn": 68}, {"ruleId": "1954", "severity": 1, "message": "2217", "line": 70, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 70, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2218", "line": 1, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2219", "line": 2, "column": 44, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 56}, {"ruleId": "1954", "severity": 1, "message": "2220", "line": 18, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2221", "line": 19, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 7}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 20, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2223", "line": 21, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 21, "endColumn": 7}, {"ruleId": "1954", "severity": 1, "message": "2224", "line": 24, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2225", "line": 25, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2226", "line": 26, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2227", "line": 31, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2228", "line": 38, "column": 49, "nodeType": "1956", "messageId": "1957", "endLine": 38, "endColumn": 55}, {"ruleId": "1954", "severity": 1, "message": "2229", "line": 38, "column": 63, "nodeType": "1956", "messageId": "1957", "endLine": 38, "endColumn": 70}, {"ruleId": "1954", "severity": 1, "message": "2230", "line": 46, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 46, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2231", "line": 48, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 48, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2232", "line": 92, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 92, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2233", "line": 93, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 93, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2234", "line": 99, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 99, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2235", "line": 100, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 100, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2236", "line": 104, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 104, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2237", "line": 108, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 108, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2238", "line": 112, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 112, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2239", "line": 113, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 113, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2240", "line": 114, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 114, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2241", "line": 115, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 115, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2242", "line": 116, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 116, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2243", "line": 120, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 120, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2244", "line": 121, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 121, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2245", "line": 165, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 165, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2246", "line": 175, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 175, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2247", "line": 183, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 183, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2248", "line": 184, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 184, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2249", "line": 185, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 185, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2250", "line": 186, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 186, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2251", "line": 187, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 187, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2252", "line": 208, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 208, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2253", "line": 212, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 212, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2254", "line": 458, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 458, "endColumn": 26}, {"ruleId": "2058", "severity": 1, "message": "2255", "line": 551, "column": 5, "nodeType": "2060", "endLine": 551, "endColumn": 60, "suggestions": "2256"}, {"ruleId": "1954", "severity": 1, "message": "2257", "line": 565, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 565, "endColumn": 22}, {"ruleId": "2058", "severity": 1, "message": "2258", "line": 586, "column": 5, "nodeType": "2060", "endLine": 586, "endColumn": 75, "suggestions": "2259"}, {"ruleId": "1954", "severity": 1, "message": "2260", "line": 587, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 587, "endColumn": 20}, {"ruleId": "2058", "severity": 1, "message": "2261", "line": 602, "column": 7, "nodeType": "2060", "endLine": 602, "endColumn": 23, "suggestions": "2262"}, {"ruleId": "2058", "severity": 1, "message": "2263", "line": 623, "column": 4, "nodeType": "2060", "endLine": 623, "endColumn": 6, "suggestions": "2264"}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2266", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 29, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 39}, {"ruleId": "1954", "severity": 1, "message": "1994", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "1995", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "1992", "line": 5, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "1993", "line": 5, "column": 46, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 55}, {"ruleId": "1954", "severity": 1, "message": "2268", "line": 6, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2269", "line": 6, "column": 24, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2007", "line": 10, "column": 31, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 52}, {"ruleId": "1954", "severity": 1, "message": "2004", "line": 11, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2270", "line": 17, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2271", "line": 21, "column": 19, "nodeType": "1956", "messageId": "1957", "endLine": 21, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2272", "line": 24, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2273", "line": 25, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2274", "line": 26, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2038", "line": 35, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 35, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2039", "line": 35, "column": 28, "nodeType": "1956", "messageId": "1957", "endLine": 35, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2275", "line": 249, "column": 12, "nodeType": "1956", "messageId": "1957", "endLine": 249, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "1969", "line": 6, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2276", "line": 9, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 7}, {"ruleId": "1954", "severity": 1, "message": "2277", "line": 12, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2278", "line": 25, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 38}, {"ruleId": "1954", "severity": 1, "message": "2279", "line": 53, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 53, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2280", "line": 54, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 54, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2076", "line": 55, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 55, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2281", "line": 56, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 56, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2282", "line": 57, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 57, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2092", "line": 58, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 58, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2283", "line": 59, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2284", "line": 60, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 60, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2093", "line": 61, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 61, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2285", "line": 62, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 62, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2286", "line": 63, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 63, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2287", "line": 64, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 64, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2288", "line": 65, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 65, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2289", "line": 66, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 66, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2239", "line": 67, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 67, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2242", "line": 68, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 68, "endColumn": 23}, {"ruleId": "2058", "severity": 1, "message": "2290", "line": 85, "column": 5, "nodeType": "2060", "endLine": 85, "endColumn": 7, "suggestions": "2291"}, {"ruleId": "2058", "severity": 1, "message": "2292", "line": 103, "column": 5, "nodeType": "2060", "endLine": 103, "endColumn": 28, "suggestions": "2293"}, {"ruleId": "2058", "severity": 1, "message": "2294", "line": 114, "column": 5, "nodeType": "2060", "endLine": 114, "endColumn": 48, "suggestions": "2295"}, {"ruleId": "1954", "severity": 1, "message": "2296", "line": 193, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 193, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2297", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2002", "line": 3, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 37}, {"ruleId": "1954", "severity": 1, "message": "2298", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2299", "line": 457, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 457, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2300", "line": 530, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 530, "endColumn": 24}, {"ruleId": "2182", "severity": 1, "message": "2183", "line": 682, "column": 41, "nodeType": "2184", "messageId": "2185", "endLine": 682, "endColumn": 42, "suggestions": "2301"}, {"ruleId": "2182", "severity": 1, "message": "2302", "line": 682, "column": 45, "nodeType": "2184", "messageId": "2185", "endLine": 682, "endColumn": 46, "suggestions": "2303"}, {"ruleId": "2182", "severity": 1, "message": "2183", "line": 682, "column": 56, "nodeType": "2184", "messageId": "2185", "endLine": 682, "endColumn": 57, "suggestions": "2304"}, {"ruleId": "2182", "severity": 1, "message": "2302", "line": 682, "column": 60, "nodeType": "2184", "messageId": "2185", "endLine": 682, "endColumn": 61, "suggestions": "2305"}, {"ruleId": "2182", "severity": 1, "message": "2183", "line": 682, "column": 89, "nodeType": "2184", "messageId": "2185", "endLine": 682, "endColumn": 90, "suggestions": "2306"}, {"ruleId": "2182", "severity": 1, "message": "2302", "line": 682, "column": 93, "nodeType": "2184", "messageId": "2185", "endLine": 682, "endColumn": 94, "suggestions": "2307"}, {"ruleId": "2182", "severity": 1, "message": "2183", "line": 682, "column": 104, "nodeType": "2184", "messageId": "2185", "endLine": 682, "endColumn": 105, "suggestions": "2308"}, {"ruleId": "2182", "severity": 1, "message": "2302", "line": 682, "column": 108, "nodeType": "2184", "messageId": "2185", "endLine": 682, "endColumn": 109, "suggestions": "2309"}, {"ruleId": "1954", "severity": 1, "message": "2310", "line": 1316, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 1316, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2311", "line": 1321, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 1321, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2312", "line": 1, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2313", "line": 3, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2297", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "1960", "line": 2, "column": 28, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2314", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2315", "line": 8, "column": 33, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2316", "line": 8, "column": 44, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 59}, {"ruleId": "1954", "severity": 1, "message": "2317", "line": 10, "column": 34, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 57}, {"ruleId": "1954", "severity": 1, "message": "2318", "line": 10, "column": 59, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 79}, {"ruleId": "1954", "severity": 1, "message": "2319", "line": 59, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2320", "line": 124, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 124, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "1960", "line": 1, "column": 28, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2321", "line": 8, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2315", "line": 9, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2322", "line": 80, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 80, "endColumn": 58}, {"ruleId": "2058", "severity": 1, "message": "2323", "line": 86, "column": 8, "nodeType": "2324", "endLine": 90, "endColumn": 12}, {"ruleId": "2058", "severity": 1, "message": "2325", "line": 86, "column": 8, "nodeType": "2324", "endLine": 90, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2326", "line": 92, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 92, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2327", "line": 92, "column": 25, "nodeType": "1956", "messageId": "1957", "endLine": 92, "endColumn": 42}, {"ruleId": "2328", "severity": 1, "message": "2329", "line": 113, "column": 113, "nodeType": "2330", "messageId": "2331", "endLine": 113, "endColumn": 397}, {"ruleId": "2058", "severity": 1, "message": "2332", "line": 154, "column": 5, "nodeType": "2060", "endLine": 154, "endColumn": 38, "suggestions": "2333"}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 154, "column": 6, "nodeType": "2107", "endLine": 154, "endColumn": 37}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 154, "column": 33, "nodeType": "2107", "messageId": "2108", "endLine": 154, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2319", "line": 156, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 156, "endColumn": 24}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 174, "column": 56, "nodeType": "2107", "messageId": "2108", "endLine": 174, "endColumn": 58}, {"ruleId": "1954", "severity": 1, "message": "2334", "line": 181, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 181, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2335", "line": 182, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 182, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2336", "line": 305, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 305, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2319", "line": 771, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 771, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2337", "line": 806, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 806, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2338", "line": 806, "column": 19, "nodeType": "1956", "messageId": "1957", "endLine": 806, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2339", "line": 807, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 807, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2340", "line": 807, "column": 22, "nodeType": "1956", "messageId": "1957", "endLine": 807, "endColumn": 36}, {"ruleId": "1954", "severity": 1, "message": "2133", "line": 808, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 808, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2134", "line": 808, "column": 29, "nodeType": "1956", "messageId": "1957", "endLine": 808, "endColumn": 48}, {"ruleId": "1954", "severity": 1, "message": "2341", "line": 809, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 809, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2342", "line": 809, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 809, "endColumn": 46}, {"ruleId": "1954", "severity": 1, "message": "2052", "line": 810, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 810, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2343", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2344", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2345", "line": 31, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2346", "line": 32, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 32, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2266", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "1964", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2270", "line": 4, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 9, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 11, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 12, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2348", "line": 14, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 14, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2226", "line": 16, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2349", "line": 18, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 19, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2351", "line": 20, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 21, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 21, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2353", "line": 22, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 23, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 23, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2355", "line": 24, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2356", "line": 25, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "1989", "line": 3, "column": 65, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 69}, {"ruleId": "1954", "severity": 1, "message": "2357", "line": 6, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2358", "line": 7, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 16, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2359", "line": 20, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2338", "line": 84, "column": 19, "nodeType": "1956", "messageId": "1957", "endLine": 84, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2360", "line": 85, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 85, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2361", "line": 85, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 85, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2362", "line": 86, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 86, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2363", "line": 86, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 86, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2364", "line": 90, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 90, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2365", "line": 90, "column": 45, "nodeType": "1956", "messageId": "1957", "endLine": 90, "endColumn": 62}, {"ruleId": "1954", "severity": 1, "message": "2366", "line": 93, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 93, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2367", "line": 94, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 94, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2045", "line": 95, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 95, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2046", "line": 96, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 96, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2047", "line": 97, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 97, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "2048", "line": 98, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 98, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2049", "line": 99, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 99, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2050", "line": 100, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 100, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2368", "line": 101, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 101, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2369", "line": 102, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 102, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2370", "line": 103, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 103, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2371", "line": 104, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 104, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2372", "line": 105, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 105, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2373", "line": 107, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 107, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2374", "line": 108, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 108, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2375", "line": 115, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 115, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2376", "line": 116, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 116, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2377", "line": 118, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 118, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2378", "line": 119, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 119, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2379", "line": 120, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 120, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2380", "line": 121, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 121, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2381", "line": 122, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 122, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2382", "line": 123, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 123, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2383", "line": 132, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 132, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2384", "line": 137, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 137, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2233", "line": 139, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 139, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2240", "line": 140, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 140, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2385", "line": 141, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 141, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2386", "line": 144, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 144, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2238", "line": 145, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 145, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2387", "line": 146, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 146, "endColumn": 20}, {"ruleId": "2058", "severity": 1, "message": "2388", "line": 170, "column": 5, "nodeType": "2060", "endLine": 170, "endColumn": 45, "suggestions": "2389"}, {"ruleId": "1954", "severity": 1, "message": "2390", "line": 221, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 221, "endColumn": 29}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 241, "column": 24, "nodeType": "2107", "messageId": "2108", "endLine": 241, "endColumn": 26}, {"ruleId": "2058", "severity": 1, "message": "2391", "line": 315, "column": 7, "nodeType": "2060", "endLine": 315, "endColumn": 42, "suggestions": "2392"}, {"ruleId": "1954", "severity": 1, "message": "2393", "line": 339, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 339, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2394", "line": 340, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 340, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2395", "line": 488, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 488, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2396", "line": 491, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 491, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2397", "line": 500, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 500, "endColumn": 31}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 811, "column": 26, "nodeType": "2107", "messageId": "2108", "endLine": 811, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2398", "line": 1031, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1031, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2399", "line": 1035, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1035, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2400", "line": 1039, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1039, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2401", "line": 1043, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1043, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2402", "line": 1047, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1047, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2403", "line": 1051, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1051, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2404", "line": 1055, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1055, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2405", "line": 1059, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1059, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2406", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2407", "line": 13, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 47}, {"ruleId": "1954", "severity": 1, "message": "2408", "line": 15, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2409", "line": 79, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 79, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2410", "line": 82, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 82, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2411", "line": 83, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 83, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2412", "line": 84, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 84, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2071", "line": 88, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 88, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2413", "line": 89, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 89, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2414", "line": 91, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 91, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2073", "line": 92, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 92, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2074", "line": 93, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 93, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2072", "line": 94, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 94, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2243", "line": 104, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 104, "endColumn": 19}, {"ruleId": "2058", "severity": 1, "message": "2415", "line": 209, "column": 7, "nodeType": "2060", "endLine": 209, "endColumn": 9, "suggestions": "2416"}, {"ruleId": "2058", "severity": 1, "message": "2417", "line": 244, "column": 7, "nodeType": "2060", "endLine": 244, "endColumn": 29, "suggestions": "2418"}, {"ruleId": "2058", "severity": 1, "message": "2419", "line": 249, "column": 7, "nodeType": "2060", "endLine": 249, "endColumn": 18, "suggestions": "2420"}, {"ruleId": "2058", "severity": 1, "message": "2421", "line": 292, "column": 7, "nodeType": "2060", "endLine": 292, "endColumn": 72, "suggestions": "2422"}, {"ruleId": "1954", "severity": 1, "message": "2372", "line": 331, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 331, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2423", "line": 334, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 334, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2424", "line": 465, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 465, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2425", "line": 4, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2426", "line": 6, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2427", "line": 7, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2428", "line": 7, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2429", "line": 8, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2430", "line": 9, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2073", "line": 13, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2074", "line": 14, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 14, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2072", "line": 15, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2431", "line": 17, "column": 21, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2432", "line": 18, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2433", "line": 18, "column": 33, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2434", "line": 19, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2435", "line": 96, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 96, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2436", "line": 97, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 97, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2437", "line": 100, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 100, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2438", "line": 101, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 101, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2439", "line": 109, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 109, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2440", "line": 129, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 129, "endColumn": 16}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 176, "column": 45, "nodeType": "2107", "messageId": "2108", "endLine": 176, "endColumn": 47}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 176, "column": 104, "nodeType": "2107", "messageId": "2108", "endLine": 176, "endColumn": 106}, {"ruleId": "1954", "severity": 1, "message": "2227", "line": 2, "column": 60, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 73}, {"ruleId": "1954", "severity": 1, "message": "2425", "line": 3, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2441", "line": 8, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2442", "line": 9, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2443", "line": 133, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 133, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2444", "line": 134, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 134, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2445", "line": 135, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 135, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2446", "line": 135, "column": 30, "nodeType": "1956", "messageId": "1957", "endLine": 135, "endColumn": 52}, {"ruleId": "1954", "severity": 1, "message": "2243", "line": 137, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 137, "endColumn": 19}, {"ruleId": "2058", "severity": 1, "message": "2447", "line": 163, "column": 8, "nodeType": "2060", "endLine": 163, "endColumn": 10, "suggestions": "2448"}, {"ruleId": "1954", "severity": 1, "message": "2449", "line": 299, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 299, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2450", "line": 342, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 342, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2451", "line": 343, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 343, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2452", "line": 344, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 344, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2453", "line": 346, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 346, "endColumn": 20}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 465, "column": 22, "nodeType": "2107", "messageId": "2108", "endLine": 465, "endColumn": 24}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 465, "column": 53, "nodeType": "2107", "messageId": "2108", "endLine": 465, "endColumn": 55}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 465, "column": 89, "nodeType": "2107", "messageId": "2108", "endLine": 465, "endColumn": 91}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 465, "column": 125, "nodeType": "2107", "messageId": "2108", "endLine": 465, "endColumn": 127}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 467, "column": 29, "nodeType": "2107", "messageId": "2108", "endLine": 467, "endColumn": 31}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 467, "column": 56, "nodeType": "2107", "messageId": "2108", "endLine": 467, "endColumn": 58}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 467, "column": 88, "nodeType": "2107", "messageId": "2108", "endLine": 467, "endColumn": 90}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 467, "column": 120, "nodeType": "2107", "messageId": "2108", "endLine": 467, "endColumn": 122}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 469, "column": 29, "nodeType": "2107", "messageId": "2108", "endLine": 469, "endColumn": 31}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 469, "column": 64, "nodeType": "2107", "messageId": "2108", "endLine": 469, "endColumn": 66}, {"ruleId": "1954", "severity": 1, "message": "2454", "line": 111, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 111, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2382", "line": 152, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 152, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2084", "line": 153, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 153, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2455", "line": 159, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 159, "endColumn": 23}, {"ruleId": "2456", "severity": 1, "message": "2457", "line": 225, "column": 25, "nodeType": "1956", "messageId": "2458", "endLine": 225, "endColumn": 34, "suggestions": "2459"}, {"ruleId": "2058", "severity": 1, "message": "2460", "line": 231, "column": 5, "nodeType": "2060", "endLine": 231, "endColumn": 12, "suggestions": "2461"}, {"ruleId": "2058", "severity": 1, "message": "2462", "line": 237, "column": 5, "nodeType": "2060", "endLine": 237, "endColumn": 21, "suggestions": "2463"}, {"ruleId": "2058", "severity": 1, "message": "2464", "line": 472, "column": 5, "nodeType": "2060", "endLine": 472, "endColumn": 70, "suggestions": "2465"}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 547, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 547, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 548, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 548, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 549, "column": 24, "nodeType": "2107", "messageId": "2108", "endLine": 549, "endColumn": 26}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 550, "column": 24, "nodeType": "2107", "messageId": "2108", "endLine": 550, "endColumn": 26}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 554, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 554, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 555, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 555, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 556, "column": 24, "nodeType": "2107", "messageId": "2108", "endLine": 556, "endColumn": 26}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 557, "column": 24, "nodeType": "2107", "messageId": "2108", "endLine": 557, "endColumn": 26}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 561, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 561, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 562, "column": 24, "nodeType": "2107", "messageId": "2108", "endLine": 562, "endColumn": 26}, {"ruleId": "2058", "severity": 1, "message": "2466", "line": 582, "column": 5, "nodeType": "2060", "endLine": 582, "endColumn": 64, "suggestions": "2467"}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 582, "column": 6, "nodeType": "2160", "endLine": 582, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2468", "line": 591, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 591, "endColumn": 21}, {"ruleId": "2058", "severity": 1, "message": "2469", "line": 605, "column": 5, "nodeType": "2060", "endLine": 605, "endColumn": 47, "suggestions": "2470"}, {"ruleId": "1954", "severity": 1, "message": "2468", "line": 614, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 614, "endColumn": 21}, {"ruleId": "2058", "severity": 1, "message": "2469", "line": 627, "column": 5, "nodeType": "2060", "endLine": 627, "endColumn": 47, "suggestions": "2471"}, {"ruleId": "2058", "severity": 1, "message": "2472", "line": 1021, "column": 17, "nodeType": "1956", "endLine": 1021, "endColumn": 32}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 1227, "column": 43, "nodeType": "2107", "messageId": "2108", "endLine": 1227, "endColumn": 45}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 1232, "column": 78, "nodeType": "2107", "messageId": "2108", "endLine": 1232, "endColumn": 80}, {"ruleId": "1954", "severity": 1, "message": "2473", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2474", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2475", "line": 2, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2197", "line": 3, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2476", "line": 11, "column": 62, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 67}, {"ruleId": "1954", "severity": 1, "message": "2288", "line": 25, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2242", "line": 28, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 28, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2477", "line": 31, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2478", "line": 144, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 144, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2479", "line": 145, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 145, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2480", "line": 1, "column": 28, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 5, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 5}, {"ruleId": "1954", "severity": 1, "message": "2481", "line": 6, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2482", "line": 10, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2483", "line": 12, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 13, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2484", "line": 17, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2321", "line": 19, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2485", "line": 34, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 34, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2339", "line": 34, "column": 33, "nodeType": "1956", "messageId": "1957", "endLine": 34, "endColumn": 44}, {"ruleId": "2486", "severity": 1, "message": "2487", "line": 96, "column": 2, "nodeType": "2488", "messageId": "2489", "endLine": 112, "endColumn": 4}, {"ruleId": "1954", "severity": 1, "message": "2490", "line": 133, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 133, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2491", "line": 136, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 136, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2369", "line": 137, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 137, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2371", "line": 138, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 138, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2492", "line": 140, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 140, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2366", "line": 141, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 141, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2493", "line": 142, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 142, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2494", "line": 145, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 145, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2495", "line": 146, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 146, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2496", "line": 147, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 147, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2497", "line": 148, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 148, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2498", "line": 149, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 149, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2499", "line": 150, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 150, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2377", "line": 151, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 151, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2376", "line": 152, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 152, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2375", "line": 153, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 153, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2500", "line": 156, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 156, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2501", "line": 159, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 159, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2502", "line": 160, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 160, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2503", "line": 161, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 161, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2281", "line": 162, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 162, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2237", "line": 163, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 163, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2504", "line": 166, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 166, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2505", "line": 167, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 167, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2455", "line": 169, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 169, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2506", "line": 174, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 174, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2084", "line": 175, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 175, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2507", "line": 177, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 177, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2508", "line": 186, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 186, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2509", "line": 186, "column": 25, "nodeType": "1956", "messageId": "1957", "endLine": 186, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2510", "line": 188, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 188, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2511", "line": 188, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 188, "endColumn": 44}, {"ruleId": "2512", "severity": 1, "message": "2513", "line": 350, "column": 5, "nodeType": "2514", "messageId": "2515", "endLine": 350, "endColumn": 52}, {"ruleId": "1954", "severity": 1, "message": "2516", "line": 505, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 505, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2517", "line": 575, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 575, "endColumn": 21}, {"ruleId": "2058", "severity": 1, "message": "2518", "line": 742, "column": 5, "nodeType": "2060", "endLine": 742, "endColumn": 100, "suggestions": "2519"}, {"ruleId": "2058", "severity": 1, "message": "2520", "line": 760, "column": 5, "nodeType": "2060", "endLine": 760, "endColumn": 83, "suggestions": "2521"}, {"ruleId": "1954", "severity": 1, "message": "2522", "line": 940, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 940, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2523", "line": 947, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 947, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2473", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2524", "line": 1, "column": 28, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 36}, {"ruleId": "1954", "severity": 1, "message": "2525", "line": 4, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2279", "line": 14, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 14, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2285", "line": 15, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2280", "line": 16, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2242", "line": 17, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2289", "line": 20, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2526", "line": 26, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2524", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2527", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2197", "line": 2, "column": 19, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 37}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 2, "column": 39, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2482", "line": 2, "column": 44, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 58}, {"ruleId": "1954", "severity": 1, "message": "2227", "line": 2, "column": 60, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 73}, {"ruleId": "1954", "severity": 1, "message": "2353", "line": 2, "column": 74, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 84}, {"ruleId": "1954", "severity": 1, "message": "2528", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2529", "line": 4, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2279", "line": 98, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 98, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2285", "line": 99, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 99, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2280", "line": 100, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 100, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2076", "line": 101, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 101, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2281", "line": 102, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 102, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2282", "line": 103, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 103, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2092", "line": 104, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 104, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2093", "line": 105, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 105, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2340", "line": 110, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 110, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2243", "line": 113, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 113, "endColumn": 29}, {"ruleId": "2058", "severity": 1, "message": "2530", "line": 172, "column": 12, "nodeType": "2060", "endLine": 172, "endColumn": 35, "suggestions": "2531"}, {"ruleId": "1954", "severity": 1, "message": "1960", "line": 1, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2190", "line": 4, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2297", "line": 7, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2002", "line": 7, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 37}, {"ruleId": "1954", "severity": 1, "message": "2532", "line": 43, "column": 21, "nodeType": "1956", "messageId": "1957", "endLine": 43, "endColumn": 34}, {"ruleId": "2058", "severity": 1, "message": "2533", "line": 63, "column": 21, "nodeType": "2534", "endLine": 63, "endColumn": 111}, {"ruleId": "1954", "severity": 1, "message": "2535", "line": 2, "column": 25, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 39}, {"ruleId": "1954", "severity": 1, "message": "2536", "line": 4, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2189", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2537", "line": 11, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2538", "line": 1, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2539", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "1991", "line": 1, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2539", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2540", "line": 2, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 40}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2541", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2541", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2353", "line": 2, "column": 50, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 60}, {"ruleId": "1954", "severity": 1, "message": "2528", "line": 29, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 29, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2269", "line": 34, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 34, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2506", "line": 67, "column": 29, "nodeType": "1956", "messageId": "1957", "endLine": 67, "endColumn": 43}, {"ruleId": "1954", "severity": 1, "message": "2542", "line": 75, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 75, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2543", "line": 77, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 77, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2544", "line": 80, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 80, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2545", "line": 81, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 81, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2546", "line": 100, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 100, "endColumn": 22}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 110, "column": 34, "nodeType": "2107", "messageId": "2108", "endLine": 110, "endColumn": 36}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 158, "column": 44, "nodeType": "2107", "messageId": "2108", "endLine": 158, "endColumn": 46}, {"ruleId": "1954", "severity": 1, "message": "2547", "line": 177, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 177, "endColumn": 21}, {"ruleId": "2058", "severity": 1, "message": "2548", "line": 306, "column": 5, "nodeType": "2060", "endLine": 306, "endColumn": 50, "suggestions": "2549"}, {"ruleId": "2058", "severity": 1, "message": "2548", "line": 322, "column": 5, "nodeType": "2060", "endLine": 322, "endColumn": 18, "suggestions": "2550"}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 484, "column": 48, "nodeType": "2107", "messageId": "2108", "endLine": 484, "endColumn": 50}, {"ruleId": "1954", "severity": 1, "message": "2551", "line": 2, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2552", "line": 6, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2553", "line": 28, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 28, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2554", "line": 81, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 81, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2555", "line": 82, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 82, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2556", "line": 83, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 83, "endColumn": 16}, {"ruleId": "2058", "severity": 1, "message": "2557", "line": 108, "column": 12, "nodeType": "2060", "endLine": 108, "endColumn": 26, "suggestions": "2558"}, {"ruleId": "2058", "severity": 1, "message": "2559", "line": 322, "column": 8, "nodeType": "2060", "endLine": 322, "endColumn": 38, "suggestions": "2560"}, {"ruleId": "1954", "severity": 1, "message": "2551", "line": 2, "column": 44, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 53}, {"ruleId": "1954", "severity": 1, "message": "2561", "line": 4, "column": 46, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 65}, {"ruleId": "1954", "severity": 1, "message": "1984", "line": 4, "column": 67, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 75}, {"ruleId": "1954", "severity": 1, "message": "2528", "line": 7, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2562", "line": 8, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2279", "line": 30, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 30, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2563", "line": 31, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2072", "line": 34, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 34, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2564", "line": 46, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 46, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2565", "line": 47, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 47, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2566", "line": 48, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 48, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2567", "line": 49, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 49, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2568", "line": 53, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 53, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2569", "line": 53, "column": 21, "nodeType": "1956", "messageId": "1957", "endLine": 53, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2570", "line": 54, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 54, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2571", "line": 55, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 55, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2572", "line": 58, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 58, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2573", "line": 59, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2574", "line": 59, "column": 20, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 32}, {"ruleId": "2058", "severity": 1, "message": "2575", "line": 67, "column": 5, "nodeType": "2060", "endLine": 67, "endColumn": 7, "suggestions": "2576"}, {"ruleId": "1954", "severity": 1, "message": "2577", "line": 95, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 95, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2578", "line": 99, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 99, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2579", "line": 126, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 126, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2580", "line": 134, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 134, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2581", "line": 138, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 138, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2582", "line": 152, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 152, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2583", "line": 155, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 155, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "1960", "line": 5, "column": 28, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2584", "line": 8, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2326", "line": 85, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 85, "endColumn": 24}, {"ruleId": "2058", "severity": 1, "message": "2585", "line": 98, "column": 6, "nodeType": "2060", "endLine": 98, "endColumn": 8, "suggestions": "2586"}, {"ruleId": "2058", "severity": 1, "message": "2587", "line": 121, "column": 6, "nodeType": "2060", "endLine": 121, "endColumn": 32, "suggestions": "2588"}, {"ruleId": "2058", "severity": 1, "message": "2332", "line": 125, "column": 6, "nodeType": "2060", "endLine": 125, "endColumn": 40, "suggestions": "2589"}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 125, "column": 7, "nodeType": "2107", "endLine": 125, "endColumn": 39}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 125, "column": 35, "nodeType": "2107", "messageId": "2108", "endLine": 125, "endColumn": 37}, {"ruleId": "2058", "severity": 1, "message": "2590", "line": 148, "column": 6, "nodeType": "2060", "endLine": 148, "endColumn": 33, "suggestions": "2591"}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 148, "column": 7, "nodeType": "2112", "endLine": 148, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2592", "line": 156, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 156, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2593", "line": 2, "column": 14, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2594", "line": 16, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2595", "line": 19, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2596", "line": 22, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 20}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 43, "column": 100, "nodeType": "2107", "messageId": "2108", "endLine": 43, "endColumn": 102}, {"ruleId": "1954", "severity": 1, "message": "2597", "line": 4, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2598", "line": 4, "column": 32, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 45}, {"ruleId": "1954", "severity": 1, "message": "2599", "line": 10, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2600", "line": 65, "column": 12, "nodeType": "1956", "messageId": "1957", "endLine": 65, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2601", "line": 65, "column": 24, "nodeType": "1956", "messageId": "1957", "endLine": 65, "endColumn": 37}, {"ruleId": "1954", "severity": 1, "message": "2602", "line": 78, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 78, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2603", "line": 78, "column": 24, "nodeType": "1956", "messageId": "1957", "endLine": 78, "endColumn": 39}, {"ruleId": "2058", "severity": 1, "message": "2604", "line": 120, "column": 6, "nodeType": "2060", "endLine": 120, "endColumn": 8, "suggestions": "2605"}, {"ruleId": "1954", "severity": 1, "message": "2606", "line": 157, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 157, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2607", "line": 280, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 280, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2608", "line": 296, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 296, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2609", "line": 461, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 461, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2610", "line": 462, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 462, "endColumn": 20}, {"ruleId": "2058", "severity": 1, "message": "2611", "line": 467, "column": 3, "nodeType": "2060", "endLine": 467, "endColumn": 5, "suggestions": "2612"}, {"ruleId": "1954", "severity": 1, "message": "2613", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 2, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 2, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 78}, {"ruleId": "1954", "severity": 1, "message": "2349", "line": 2, "column": 80, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 2, "column": 93, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 103}, {"ruleId": "1954", "severity": 1, "message": "2351", "line": 2, "column": 105, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 111}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 2, "column": 113, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 121}, {"ruleId": "1954", "severity": 1, "message": "2614", "line": 2, "column": 123, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 140}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 2, "column": 142, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 158}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 2, "column": 160, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 166}, {"ruleId": "1954", "severity": 1, "message": "2616", "line": 2, "column": 168, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 180}, {"ruleId": "1954", "severity": 1, "message": "2617", "line": 2, "column": 182, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 199}, {"ruleId": "1954", "severity": 1, "message": "2618", "line": 4, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 49}, {"ruleId": "1954", "severity": 1, "message": "2619", "line": 4, "column": 51, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2620", "line": 4, "column": 73, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2621", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2622", "line": 13, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2623", "line": 14, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 14, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2624", "line": 15, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "1984", "line": 16, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2625", "line": 17, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 7}, {"ruleId": "1954", "severity": 1, "message": "2626", "line": 24, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2627", "line": 25, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2628", "line": 26, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2629", "line": 27, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 27, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2630", "line": 28, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 28, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2631", "line": 29, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 29, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2632", "line": 30, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 30, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2633", "line": 40, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 40, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2634", "line": 41, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 41, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2635", "line": 43, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 43, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2636", "line": 45, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 45, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2334", "line": 47, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 47, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2637", "line": 94, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 94, "endColumn": 19}, {"ruleId": "2058", "severity": 1, "message": "2638", "line": 125, "column": 5, "nodeType": "2060", "endLine": 125, "endColumn": 7, "suggestions": "2639"}, {"ruleId": "1954", "severity": 1, "message": "2640", "line": 145, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 145, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2641", "line": 162, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 162, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2642", "line": 165, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 165, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2643", "line": 170, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 170, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2644", "line": 211, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 211, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2645", "line": 214, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 214, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2646", "line": 227, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 227, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2647", "line": 228, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 228, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2648", "line": 228, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 228, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2649", "line": 229, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 229, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2650", "line": 229, "column": 20, "nodeType": "1956", "messageId": "1957", "endLine": 229, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2040", "line": 245, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 245, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2041", "line": 245, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 245, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2651", "line": 247, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 247, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2652", "line": 261, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 261, "endColumn": 36}, {"ruleId": "2058", "severity": 1, "message": "2653", "line": 281, "column": 4, "nodeType": "2060", "endLine": 281, "endColumn": 6, "suggestions": "2654"}, {"ruleId": "1954", "severity": 1, "message": "2652", "line": 334, "column": 12, "nodeType": "1956", "messageId": "1957", "endLine": 334, "endColumn": 38}, {"ruleId": "1954", "severity": 1, "message": "2655", "line": 347, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 347, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2656", "line": 347, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 347, "endColumn": 45}, {"ruleId": "1954", "severity": 1, "message": "2613", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 2, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 2, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 78}, {"ruleId": "1954", "severity": 1, "message": "2349", "line": 2, "column": 80, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 2, "column": 93, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 103}, {"ruleId": "1954", "severity": 1, "message": "2351", "line": 2, "column": 105, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 111}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 2, "column": 113, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 121}, {"ruleId": "1954", "severity": 1, "message": "2614", "line": 2, "column": 123, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 140}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 2, "column": 142, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 158}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 2, "column": 160, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 166}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 2, "column": 168, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 175}, {"ruleId": "1954", "severity": 1, "message": "2618", "line": 4, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 49}, {"ruleId": "1954", "severity": 1, "message": "2619", "line": 4, "column": 51, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2620", "line": 4, "column": 73, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2621", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2622", "line": 8, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2623", "line": 9, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2624", "line": 10, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2625", "line": 11, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "1984", "line": 12, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2657", "line": 13, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2658", "line": 14, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 14, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2659", "line": 15, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2660", "line": 22, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2661", "line": 31, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2317", "line": 32, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 32, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2633", "line": 35, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 35, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2634", "line": 36, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 36, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2635", "line": 38, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 38, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2662", "line": 39, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 39, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2663", "line": 40, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 40, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2664", "line": 42, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 42, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2665", "line": 43, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 43, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2666", "line": 44, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 44, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2667", "line": 45, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 45, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2668", "line": 46, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 46, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2669", "line": 47, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 47, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2670", "line": 48, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 48, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2671", "line": 49, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 49, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2672", "line": 50, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 50, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2673", "line": 51, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 51, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2542", "line": 58, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 58, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2595", "line": 60, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 60, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2637", "line": 75, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 75, "endColumn": 19}, {"ruleId": "2058", "severity": 1, "message": "2590", "line": 87, "column": 5, "nodeType": "2060", "endLine": 87, "endColumn": 45, "suggestions": "2674"}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 87, "column": 6, "nodeType": "2160", "endLine": 87, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2675", "line": 106, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 106, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2676", "line": 106, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 106, "endColumn": 38}, {"ruleId": "1954", "severity": 1, "message": "2677", "line": 107, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 107, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2678", "line": 107, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 107, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2679", "line": 108, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 108, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2236", "line": 109, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 109, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2680", "line": 109, "column": 18, "nodeType": "1956", "messageId": "1957", "endLine": 109, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2681", "line": 110, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 110, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2641", "line": 115, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 115, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2643", "line": 119, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 119, "endColumn": 25}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 134, "column": 11, "nodeType": "2107", "messageId": "2108", "endLine": 134, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 2, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2682", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2683", "line": 5, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2684", "line": 10, "column": 30, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 39}, {"ruleId": "1954", "severity": 1, "message": "2685", "line": 203, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 203, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2616", "line": 2, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 39}, {"ruleId": "1954", "severity": 1, "message": "2617", "line": 2, "column": 41, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 58}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 2, "column": 72, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 88}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 2, "column": 90, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 96}, {"ruleId": "1954", "severity": 1, "message": "2686", "line": 9, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 6, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2351", "line": 9, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 10, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2349", "line": 11, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 12, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "2687", "line": 19, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2387", "line": 35, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 35, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2688", "line": 37, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 37, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2485", "line": 38, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 38, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2339", "line": 39, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 39, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2689", "line": 40, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 40, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2238", "line": 42, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 42, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2243", "line": 48, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 48, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2690", "line": 55, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 55, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2691", "line": 56, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 56, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2692", "line": 57, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 57, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2693", "line": 86, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 86, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2694", "line": 90, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 90, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2695", "line": 95, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 95, "endColumn": 33}, {"ruleId": "2058", "severity": 1, "message": "2696", "line": 195, "column": 5, "nodeType": "2060", "endLine": 195, "endColumn": 30, "suggestions": "2697"}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 3, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 5}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 4, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 9, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 2, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 2, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2682", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 32}, {"ruleId": "1954", "severity": 1, "message": "2683", "line": 5, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2698", "line": 9, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2699", "line": 9, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2684", "line": 9, "column": 32, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 41}, {"ruleId": "1954", "severity": 1, "message": "2700", "line": 9, "column": 43, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 51}, {"ruleId": "1954", "severity": 1, "message": "2701", "line": 9, "column": 53, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 63}, {"ruleId": "1954", "severity": 1, "message": "2702", "line": 9, "column": 65, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 77}, {"ruleId": "1954", "severity": 1, "message": "2703", "line": 9, "column": 79, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 90}, {"ruleId": "1954", "severity": 1, "message": "2704", "line": 9, "column": 92, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 102}, {"ruleId": "1954", "severity": 1, "message": "2705", "line": 9, "column": 104, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 116}, {"ruleId": "1954", "severity": 1, "message": "2706", "line": 9, "column": 118, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 129}, {"ruleId": "1954", "severity": 1, "message": "2707", "line": 9, "column": 131, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 140}, {"ruleId": "1954", "severity": 1, "message": "2708", "line": 15, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2369", "line": 16, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2493", "line": 17, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2709", "line": 18, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2371", "line": 19, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2492", "line": 22, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2710", "line": 23, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 23, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2711", "line": 24, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2712", "line": 25, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2713", "line": 26, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2714", "line": 27, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 27, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2715", "line": 28, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 28, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2716", "line": 29, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 29, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2542", "line": 33, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 33, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2717", "line": 35, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 35, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2637", "line": 63, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 63, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2694", "line": 83, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 83, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2718", "line": 84, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 84, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2613", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 3, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 3, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 78}, {"ruleId": "1954", "severity": 1, "message": "2349", "line": 3, "column": 80, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 3, "column": 93, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 103}, {"ruleId": "1954", "severity": 1, "message": "2351", "line": 3, "column": 105, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 111}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 3, "column": 113, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 121}, {"ruleId": "1954", "severity": 1, "message": "2614", "line": 3, "column": 123, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 140}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 3, "column": 142, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 158}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 3, "column": 160, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 166}, {"ruleId": "1954", "severity": 1, "message": "2618", "line": 5, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 49}, {"ruleId": "1954", "severity": 1, "message": "2619", "line": 5, "column": 51, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2620", "line": 5, "column": 73, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2621", "line": 6, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2622", "line": 8, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2623", "line": 9, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2624", "line": 10, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2625", "line": 11, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2633", "line": 19, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2635", "line": 22, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2719", "line": 24, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2720", "line": 25, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2721", "line": 26, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2722", "line": 27, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 27, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2542", "line": 31, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2637", "line": 36, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 36, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2723", "line": 104, "column": 21, "nodeType": "1956", "messageId": "1957", "endLine": 104, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2724", "line": 105, "column": 24, "nodeType": "1956", "messageId": "1957", "endLine": 105, "endColumn": 40}, {"ruleId": "1954", "severity": 1, "message": "2640", "line": 120, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 120, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2641", "line": 154, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 154, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2725", "line": 157, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 157, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 3, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 3, "column": 56, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 63}, {"ruleId": "1954", "severity": 1, "message": "2239", "line": 13, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2366", "line": 14, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 14, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2367", "line": 15, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2045", "line": 16, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2047", "line": 18, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "2048", "line": 19, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2049", "line": 20, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2050", "line": 21, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 21, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2368", "line": 22, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2369", "line": 23, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 23, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2370", "line": 24, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2371", "line": 25, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2726", "line": 37, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 37, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2409", "line": 39, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 39, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2727", "line": 41, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 41, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2728", "line": 46, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 46, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2729", "line": 50, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 50, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2730", "line": 50, "column": 25, "nodeType": "1956", "messageId": "1957", "endLine": 50, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2731", "line": 51, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 51, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2732", "line": 51, "column": 21, "nodeType": "1956", "messageId": "1957", "endLine": 51, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2733", "line": 52, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 52, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2734", "line": 52, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 52, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2735", "line": 53, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 53, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2736", "line": 53, "column": 30, "nodeType": "1956", "messageId": "1957", "endLine": 53, "endColumn": 52}, {"ruleId": "1954", "severity": 1, "message": "2737", "line": 54, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 54, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2738", "line": 54, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 54, "endColumn": 46}, {"ruleId": "1954", "severity": 1, "message": "2739", "line": 3, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2740", "line": 4, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2741", "line": 5, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2742", "line": 6, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2527", "line": 9, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "2220", "line": 17, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2221", "line": 18, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 7}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 19, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2223", "line": 20, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 7}, {"ruleId": "1954", "severity": 1, "message": "2224", "line": 23, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 23, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2225", "line": 24, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2226", "line": 25, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 26, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "2426", "line": 43, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 43, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2743", "line": 44, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 44, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2744", "line": 50, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 50, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2745", "line": 51, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 51, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2746", "line": 53, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 53, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2747", "line": 54, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 54, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2748", "line": 55, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 55, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2749", "line": 56, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 56, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2279", "line": 59, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2563", "line": 60, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 60, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2233", "line": 62, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 62, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2234", "line": 68, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 68, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2235", "line": 69, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 69, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2236", "line": 75, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 75, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2750", "line": 77, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 77, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2073", "line": 80, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 80, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2074", "line": 81, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 81, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2072", "line": 82, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 82, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2751", "line": 83, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 83, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2752", "line": 84, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 84, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2753", "line": 85, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 85, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2249", "line": 87, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 87, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2247", "line": 89, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 89, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2251", "line": 91, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 91, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2754", "line": 92, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 92, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2244", "line": 95, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 95, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2369", "line": 104, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 104, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2368", "line": 104, "column": 22, "nodeType": "1956", "messageId": "1957", "endLine": 104, "endColumn": 36}, {"ruleId": "1954", "severity": 1, "message": "2371", "line": 105, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 105, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2370", "line": 105, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 105, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2755", "line": 106, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 106, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2756", "line": 107, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 107, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2757", "line": 108, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 108, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2758", "line": 108, "column": 14, "nodeType": "1956", "messageId": "1957", "endLine": 108, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2245", "line": 109, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 109, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2759", "line": 109, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 109, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2453", "line": 110, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 110, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2760", "line": 110, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 110, "endColumn": 38}, {"ruleId": "1954", "severity": 1, "message": "2692", "line": 121, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 121, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2761", "line": 121, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 121, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2762", "line": 128, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 128, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2763", "line": 128, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 128, "endColumn": 44}, {"ruleId": "2058", "severity": 1, "message": "2764", "line": 151, "column": 5, "nodeType": "2060", "endLine": 151, "endColumn": 60, "suggestions": "2765"}, {"ruleId": "2058", "severity": 1, "message": "2766", "line": 166, "column": 5, "nodeType": "2060", "endLine": 166, "endColumn": 20, "suggestions": "2767"}, {"ruleId": "1954", "severity": 1, "message": "2260", "line": 168, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 168, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2768", "line": 178, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 178, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2769", "line": 190, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 190, "endColumn": 24}, {"ruleId": "2058", "severity": 1, "message": "2770", "line": 194, "column": 5, "nodeType": "2060", "endLine": 194, "endColumn": 60, "suggestions": "2771"}, {"ruleId": "1954", "severity": 1, "message": "2772", "line": 196, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 196, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2252", "line": 230, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 230, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2253", "line": 234, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 234, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2551", "line": 2, "column": 56, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 65}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 2, "column": 67, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 75}, {"ruleId": "1954", "severity": 1, "message": "2197", "line": 2, "column": 77, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 83}, {"ruleId": "1954", "severity": 1, "message": "2561", "line": 13, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2773", "line": 47, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 47, "endColumn": 48}, {"ruleId": "1954", "severity": 1, "message": "2449", "line": 59, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 39}, {"ruleId": "1954", "severity": 1, "message": "2774", "line": 68, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 68, "endColumn": 41}, {"ruleId": "1954", "severity": 1, "message": "2775", "line": 74, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 74, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2613", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2473", "line": 1, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 47}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 2, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 2, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 78}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 2, "column": 93, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 103}, {"ruleId": "1954", "severity": 1, "message": "2614", "line": 2, "column": 123, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 140}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 2, "column": 142, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 158}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 2, "column": 160, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 166}, {"ruleId": "1954", "severity": 1, "message": "2618", "line": 4, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 49}, {"ruleId": "1954", "severity": 1, "message": "2619", "line": 4, "column": 51, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2620", "line": 4, "column": 73, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2621", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2776", "line": 23, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 23, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2081", "line": 24, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2777", "line": 26, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 26, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2778", "line": 27, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 27, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2779", "line": 28, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 28, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2780", "line": 29, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 29, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2394", "line": 85, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 85, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2425", "line": 4, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2073", "line": 11, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2074", "line": 12, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2072", "line": 13, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2431", "line": 21, "column": 21, "nodeType": "1956", "messageId": "1957", "endLine": 21, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2432", "line": 22, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2433", "line": 22, "column": 33, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 44}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 82, "column": 22, "nodeType": "2107", "messageId": "2108", "endLine": 82, "endColumn": 24}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 82, "column": 53, "nodeType": "2107", "messageId": "2108", "endLine": 82, "endColumn": 55}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 85, "column": 36, "nodeType": "2107", "messageId": "2108", "endLine": 85, "endColumn": 38}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 85, "column": 63, "nodeType": "2107", "messageId": "2108", "endLine": 85, "endColumn": 65}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 88, "column": 36, "nodeType": "2107", "messageId": "2108", "endLine": 88, "endColumn": 38}, {"ruleId": "1954", "severity": 1, "message": "2435", "line": 95, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 95, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2436", "line": 96, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 96, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2437", "line": 99, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 99, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2438", "line": 100, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 100, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2439", "line": 108, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 108, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2440", "line": 128, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 128, "endColumn": 16}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 268, "column": 45, "nodeType": "2107", "messageId": "2108", "endLine": 268, "endColumn": 47}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 268, "column": 104, "nodeType": "2107", "messageId": "2108", "endLine": 268, "endColumn": 106}, {"ruleId": "1954", "severity": 1, "message": "2781", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2481", "line": 4, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2551", "line": 8, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2782", "line": 15, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2783", "line": 15, "column": 30, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2784", "line": 15, "column": 46, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 60}, {"ruleId": "1954", "severity": 1, "message": "2785", "line": 15, "column": 62, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 78}, {"ruleId": "1954", "severity": 1, "message": "2786", "line": 15, "column": 80, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 96}, {"ruleId": "1954", "severity": 1, "message": "2787", "line": 17, "column": 29, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2788", "line": 17, "column": 35, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 47}, {"ruleId": "1954", "severity": 1, "message": "2228", "line": 17, "column": 49, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 55}, {"ruleId": "1954", "severity": 1, "message": "2789", "line": 22, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2790", "line": 58, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 58, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2791", "line": 66, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 66, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2493", "line": 72, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 72, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2492", "line": 73, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 73, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2711", "line": 74, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 74, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2369", "line": 75, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 75, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2792", "line": 76, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 76, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2500", "line": 77, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 77, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2793", "line": 78, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 78, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2501", "line": 79, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 79, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2496", "line": 80, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 80, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2794", "line": 81, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 81, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2713", "line": 82, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 82, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2237", "line": 83, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 83, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2232", "line": 84, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 84, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2795", "line": 87, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 87, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2796", "line": 89, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 89, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2238", "line": 91, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 91, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2243", "line": 93, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 93, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2797", "line": 166, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 166, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2798", "line": 169, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 169, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2799", "line": 179, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 179, "endColumn": 21}, {"ruleId": "2058", "severity": 1, "message": "2800", "line": 332, "column": 5, "nodeType": "2060", "endLine": 332, "endColumn": 18, "suggestions": "2801"}, {"ruleId": "1954", "severity": 1, "message": "2503", "line": 599, "column": 33, "nodeType": "1956", "messageId": "1957", "endLine": 599, "endColumn": 47}, {"ruleId": "1954", "severity": 1, "message": "2339", "line": 599, "column": 49, "nodeType": "1956", "messageId": "1957", "endLine": 599, "endColumn": 60}, {"ruleId": "1954", "severity": 1, "message": "2281", "line": 599, "column": 62, "nodeType": "1956", "messageId": "1957", "endLine": 599, "endColumn": 67}, {"ruleId": "1954", "severity": 1, "message": "2237", "line": 599, "column": 69, "nodeType": "1956", "messageId": "1957", "endLine": 599, "endColumn": 85}, {"ruleId": "2058", "severity": 1, "message": "2802", "line": 927, "column": 3, "nodeType": "2060", "endLine": 927, "endColumn": 19, "suggestions": "2803"}, {"ruleId": "1954", "severity": 1, "message": "2804", "line": 1000, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 1000, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2805", "line": 1009, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 1009, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2483", "line": 11, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2425", "line": 14, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 14, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2806", "line": 67, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 67, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2807", "line": 180, "column": 86, "nodeType": "1956", "messageId": "1957", "endLine": 180, "endColumn": 101}, {"ruleId": "1954", "severity": 1, "message": "2501", "line": 184, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 184, "endColumn": 24}, {"ruleId": "2058", "severity": 1, "message": "2808", "line": 563, "column": 5, "nodeType": "2060", "endLine": 563, "endColumn": 40, "suggestions": "2809"}, {"ruleId": "2058", "severity": 1, "message": "2810", "line": 586, "column": 6, "nodeType": "2060", "endLine": 586, "endColumn": 42, "suggestions": "2811"}, {"ruleId": "2058", "severity": 1, "message": "2812", "line": 600, "column": 6, "nodeType": "2060", "endLine": 600, "endColumn": 50, "suggestions": "2813"}, {"ruleId": "2058", "severity": 1, "message": "2814", "line": 877, "column": 5, "nodeType": "2060", "endLine": 877, "endColumn": 160, "suggestions": "2815"}, {"ruleId": "2058", "severity": 1, "message": "2816", "line": 945, "column": 5, "nodeType": "2060", "endLine": 945, "endColumn": 110, "suggestions": "2817"}, {"ruleId": "2058", "severity": 1, "message": "2818", "line": 993, "column": 5, "nodeType": "2060", "endLine": 993, "endColumn": 34, "suggestions": "2819"}, {"ruleId": "2058", "severity": 1, "message": "2820", "line": 1011, "column": 5, "nodeType": "2060", "endLine": 1011, "endColumn": 34, "suggestions": "2821"}, {"ruleId": "2058", "severity": 1, "message": "2820", "line": 1025, "column": 5, "nodeType": "2060", "endLine": 1025, "endColumn": 34, "suggestions": "2822"}, {"ruleId": "2058", "severity": 1, "message": "2820", "line": 1028, "column": 5, "nodeType": "2060", "endLine": 1028, "endColumn": 40, "suggestions": "2823"}, {"ruleId": "1954", "severity": 1, "message": "2824", "line": 1238, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 1238, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2825", "line": 1241, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 1241, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 2, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 2, "column": 73, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 79}, {"ruleId": "1954", "severity": 1, "message": "2004", "line": 15, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "2826", "line": 18, "column": 48, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 76}, {"ruleId": "1954", "severity": 1, "message": "2827", "line": 18, "column": 78, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 85}, {"ruleId": "1954", "severity": 1, "message": "2359", "line": 20, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2828", "line": 52, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 52, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2829", "line": 54, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 54, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2830", "line": 60, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 60, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2796", "line": 60, "column": 18, "nodeType": "1956", "messageId": "1957", "endLine": 60, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2831", "line": 61, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 61, "endColumn": 46}, {"ruleId": "1954", "severity": 1, "message": "2040", "line": 62, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 62, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2041", "line": 62, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 62, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2772", "line": 86, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 86, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2643", "line": 93, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 93, "endColumn": 25}, {"ruleId": "2058", "severity": 1, "message": "2832", "line": 185, "column": 5, "nodeType": "2060", "endLine": 185, "endColumn": 52, "suggestions": "2833"}, {"ruleId": "2058", "severity": 1, "message": "2111", "line": 185, "column": 6, "nodeType": "2160", "endLine": 185, "endColumn": 51}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 92, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 102}, {"ruleId": "1954", "severity": 1, "message": "2834", "line": 76, "column": 19, "nodeType": "1956", "messageId": "1957", "endLine": 76, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2527", "line": 2, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2353", "line": 2, "column": 36, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 46}, {"ruleId": "1954", "severity": 1, "message": "2551", "line": 2, "column": 48, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 57}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 2, "column": 59, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 67}, {"ruleId": "1954", "severity": 1, "message": "2197", "line": 2, "column": 69, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 75}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 2, "column": 77, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 84}, {"ruleId": "1954", "severity": 1, "message": "2835", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2836", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2837", "line": 8, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2838", "line": 9, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2613", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2524", "line": 1, "column": 29, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 37}, {"ruleId": "1954", "severity": 1, "message": "2473", "line": 1, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 47}, {"ruleId": "1954", "severity": 1, "message": "2266", "line": 1, "column": 49, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 59}, {"ruleId": "1954", "severity": 1, "message": "2480", "line": 1, "column": 61, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 67}, {"ruleId": "1954", "severity": 1, "message": "2551", "line": 2, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 36}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 2, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2197", "line": 2, "column": 56, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 62}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 2, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 78}, {"ruleId": "1954", "severity": 1, "message": "2349", "line": 2, "column": 80, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 2, "column": 93, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 103}, {"ruleId": "1954", "severity": 1, "message": "2351", "line": 2, "column": 105, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 111}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 2, "column": 113, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 121}, {"ruleId": "1954", "severity": 1, "message": "2614", "line": 2, "column": 123, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 140}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 2, "column": 142, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 158}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 2, "column": 160, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 166}, {"ruleId": "1954", "severity": 1, "message": "2528", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2297", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2618", "line": 4, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 49}, {"ruleId": "1954", "severity": 1, "message": "2619", "line": 4, "column": 51, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2620", "line": 4, "column": 73, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2621", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2622", "line": 7, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2623", "line": 8, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2624", "line": 9, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2625", "line": 10, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "1984", "line": 11, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2657", "line": 12, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2839", "line": 19, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2613", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 2, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 2, "column": 64, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 78}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 2, "column": 93, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 103}, {"ruleId": "1954", "severity": 1, "message": "2614", "line": 2, "column": 123, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 140}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 2, "column": 142, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 158}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 2, "column": 160, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 166}, {"ruleId": "1954", "severity": 1, "message": "2225", "line": 2, "column": 177, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 193}, {"ruleId": "1954", "severity": 1, "message": "2618", "line": 4, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 49}, {"ruleId": "1954", "severity": 1, "message": "2619", "line": 4, "column": 51, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2620", "line": 4, "column": 73, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2621", "line": 5, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2622", "line": 7, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2623", "line": 8, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2624", "line": 9, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2625", "line": 10, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "1984", "line": 11, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2627", "line": 27, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 27, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2628", "line": 28, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 28, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2629", "line": 29, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 29, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2630", "line": 30, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 30, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2633", "line": 38, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 38, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2634", "line": 39, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 39, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2635", "line": 41, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 41, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2662", "line": 42, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 42, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2663", "line": 43, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 43, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2840", "line": 44, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 44, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2664", "line": 45, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 45, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2666", "line": 47, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 47, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2667", "line": 48, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 48, "endColumn": 33}, {"ruleId": "1954", "severity": 1, "message": "2668", "line": 49, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 49, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2670", "line": 51, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 51, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2671", "line": 52, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 52, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2672", "line": 53, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 53, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2673", "line": 54, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 54, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2542", "line": 58, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 58, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2841", "line": 152, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 152, "endColumn": 40}, {"ruleId": "1954", "severity": 1, "message": "2842", "line": 153, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 153, "endColumn": 41}, {"ruleId": "1954", "severity": 1, "message": "2843", "line": 154, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 154, "endColumn": 47}, {"ruleId": "1954", "severity": 1, "message": "2040", "line": 156, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 156, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2844", "line": 185, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 185, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2641", "line": 220, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 220, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2642", "line": 223, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 223, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2643", "line": 228, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 228, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2144", "line": 245, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 245, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2675", "line": 249, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 249, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2681", "line": 254, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 254, "endColumn": 20}, {"ruleId": "2058", "severity": 1, "message": "2845", "line": 263, "column": 6, "nodeType": "2060", "endLine": 263, "endColumn": 8, "suggestions": "2846"}, {"ruleId": "1954", "severity": 1, "message": "2847", "line": 298, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 298, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2613", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2480", "line": 1, "column": 49, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 55}, {"ruleId": "1954", "severity": 1, "message": "2848", "line": 1, "column": 69, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 80}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 2, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 2, "column": 93, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 103}, {"ruleId": "1954", "severity": 1, "message": "2614", "line": 2, "column": 123, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 140}, {"ruleId": "1954", "severity": 1, "message": "2222", "line": 2, "column": 142, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 158}, {"ruleId": "1954", "severity": 1, "message": "2615", "line": 2, "column": 160, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 166}, {"ruleId": "1954", "severity": 1, "message": "2481", "line": 2, "column": 195, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 212}, {"ruleId": "1954", "severity": 1, "message": "2618", "line": 5, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 49}, {"ruleId": "1954", "severity": 1, "message": "2619", "line": 5, "column": 51, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 71}, {"ruleId": "1954", "severity": 1, "message": "2620", "line": 5, "column": 73, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 91}, {"ruleId": "1954", "severity": 1, "message": "2621", "line": 6, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2626", "line": 8, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2627", "line": 9, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2628", "line": 10, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2629", "line": 11, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2630", "line": 12, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2631", "line": 13, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2622", "line": 15, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2623", "line": 16, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2624", "line": 17, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 17, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2625", "line": 18, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "1984", "line": 19, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2836", "line": 34, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 34, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2849", "line": 44, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 44, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2633", "line": 45, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 45, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2634", "line": 46, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 46, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2635", "line": 48, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 48, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2662", "line": 49, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 49, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2663", "line": 50, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 50, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2840", "line": 51, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 51, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2664", "line": 52, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 52, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2666", "line": 54, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 54, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2667", "line": 55, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 55, "endColumn": 38}, {"ruleId": "1954", "severity": 1, "message": "2668", "line": 56, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 56, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2670", "line": 58, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 58, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2671", "line": 59, "column": 6, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2672", "line": 60, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 60, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2673", "line": 61, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 61, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2850", "line": 63, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 63, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2542", "line": 66, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 66, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2841", "line": 89, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 89, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2842", "line": 90, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 90, "endColumn": 43}, {"ruleId": "1954", "severity": 1, "message": "2843", "line": 91, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 91, "endColumn": 46}, {"ruleId": "2058", "severity": 1, "message": "2851", "line": 112, "column": 5, "nodeType": "2060", "endLine": 112, "endColumn": 52, "suggestions": "2852"}, {"ruleId": "1954", "severity": 1, "message": "2641", "line": 117, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 117, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2642", "line": 120, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 120, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2643", "line": 125, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 125, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2853", "line": 135, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 135, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2144", "line": 175, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 175, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2854", "line": 183, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 183, "endColumn": 20}, {"ruleId": "2058", "severity": 1, "message": "2845", "line": 188, "column": 4, "nodeType": "2060", "endLine": 188, "endColumn": 6, "suggestions": "2855"}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 193, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 193, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 194, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 194, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 194, "column": 39, "nodeType": "2107", "messageId": "2108", "endLine": 194, "endColumn": 41}, {"ruleId": "2105", "severity": 1, "message": "2106", "line": 213, "column": 19, "nodeType": "2107", "messageId": "2108", "endLine": 213, "endColumn": 21}, {"ruleId": "2105", "severity": 1, "message": "2127", "line": 226, "column": 20, "nodeType": "2107", "messageId": "2108", "endLine": 226, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2856", "line": 279, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 279, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2040", "line": 307, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 307, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2844", "line": 334, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 334, "endColumn": 23}, {"ruleId": "2058", "severity": 1, "message": "2857", "line": 371, "column": 4, "nodeType": "2060", "endLine": 371, "endColumn": 6, "suggestions": "2858"}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2551", "line": 2, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 36}, {"ruleId": "1954", "severity": 1, "message": "2347", "line": 2, "column": 38, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2369", "line": 9, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2368", "line": 9, "column": 22, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 36}, {"ruleId": "1954", "severity": 1, "message": "2371", "line": 10, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2370", "line": 10, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 10, "endColumn": 44}, {"ruleId": "1954", "severity": 1, "message": "2756", "line": 12, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2831", "line": 12, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 46}, {"ruleId": "1954", "severity": 1, "message": "2757", "line": 13, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2758", "line": 13, "column": 14, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2453", "line": 15, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2692", "line": 16, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2859", "line": 28, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 28, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2860", "line": 3, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 3, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2626", "line": 6, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 32, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 42}, {"ruleId": "1954", "severity": 1, "message": "2551", "line": 2, "column": 44, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 53}, {"ruleId": "1954", "severity": 1, "message": "2561", "line": 4, "column": 46, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 65}, {"ruleId": "1954", "severity": 1, "message": "1984", "line": 4, "column": 67, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 75}, {"ruleId": "1954", "severity": 1, "message": "2861", "line": 8, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2790", "line": 16, "column": 13, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2862", "line": 31, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2384", "line": 33, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 33, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2863", "line": 44, "column": 6, "nodeType": "1956", "messageId": "1957", "endLine": 44, "endColumn": 34}, {"ruleId": "1954", "severity": 1, "message": "2864", "line": 86, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 86, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2582", "line": 96, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 96, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2837", "line": 5, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2838", "line": 6, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2865", "line": 7, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2866", "line": 27, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 27, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2867", "line": 34, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 34, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2868", "line": 56, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 56, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2869", "line": 58, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 58, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2870", "line": 80, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 80, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2871", "line": 82, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 82, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2872", "line": 82, "column": 23, "nodeType": "1956", "messageId": "1957", "endLine": 82, "endColumn": 38}, {"ruleId": "1954", "severity": 1, "message": "2873", "line": 133, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 133, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2874", "line": 290, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 290, "endColumn": 28}, {"ruleId": "2058", "severity": 1, "message": "2875", "line": 344, "column": 5, "nodeType": "2060", "endLine": 344, "endColumn": 22, "suggestions": "2876"}, {"ruleId": "1954", "severity": 1, "message": "2781", "line": 1, "column": 58, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 67}, {"ruleId": "1954", "severity": 1, "message": "1960", "line": 1, "column": 75, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 82}, {"ruleId": "1954", "severity": 1, "message": "2527", "line": 2, "column": 15, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2267", "line": 2, "column": 33, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 43}, {"ruleId": "1954", "severity": 1, "message": "2789", "line": 4, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2219", "line": 5, "column": 41, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 53}, {"ruleId": "1954", "severity": 1, "message": "2787", "line": 6, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2228", "line": 6, "column": 16, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2877", "line": 6, "column": 24, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 29}, {"ruleId": "1954", "severity": 1, "message": "2878", "line": 6, "column": 31, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2406", "line": 6, "column": 37, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 47}, {"ruleId": "1954", "severity": 1, "message": "2788", "line": 6, "column": 49, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 61}, {"ruleId": "1954", "severity": 1, "message": "2528", "line": 7, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2836", "line": 8, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2879", "line": 39, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 39, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2553", "line": 40, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 40, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2880", "line": 41, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 41, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2881", "line": 43, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 43, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2412", "line": 44, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 44, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2882", "line": 45, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 45, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2411", "line": 46, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 46, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2410", "line": 47, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 47, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2883", "line": 48, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 48, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2409", "line": 50, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 50, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2884", "line": 51, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 51, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2885", "line": 52, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 52, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2490", "line": 55, "column": 4, "nodeType": "1956", "messageId": "1957", "endLine": 55, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2337", "line": 59, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2338", "line": 59, "column": 20, "nodeType": "1956", "messageId": "1957", "endLine": 59, "endColumn": 31}, {"ruleId": "1954", "severity": 1, "message": "2886", "line": 61, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 61, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2887", "line": 61, "column": 26, "nodeType": "1956", "messageId": "1957", "endLine": 61, "endColumn": 43}, {"ruleId": "1954", "severity": 1, "message": "2888", "line": 77, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 77, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2889", "line": 85, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 85, "endColumn": 29}, {"ruleId": "2058", "severity": 1, "message": "2890", "line": 118, "column": 6, "nodeType": "2060", "endLine": 118, "endColumn": 35, "suggestions": "2891"}, {"ruleId": "1954", "severity": 1, "message": "2224", "line": 2, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2892", "line": 20, "column": 24, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 35}, {"ruleId": "1954", "severity": 1, "message": "2381", "line": 42, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 42, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2485", "line": 43, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 43, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2240", "line": 44, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 44, "endColumn": 28}, {"ruleId": "1954", "severity": 1, "message": "2893", "line": 46, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 46, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2340", "line": 48, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 48, "endColumn": 17}, {"ruleId": "1954", "severity": 1, "message": "2384", "line": 49, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 49, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2364", "line": 63, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 63, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2365", "line": 63, "column": 45, "nodeType": "1956", "messageId": "1957", "endLine": 63, "endColumn": 62}, {"ruleId": "1954", "severity": 1, "message": "2894", "line": 103, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 103, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2895", "line": 106, "column": 12, "nodeType": "1956", "messageId": "1957", "endLine": 106, "endColumn": 26}, {"ruleId": "2058", "severity": 1, "message": "2587", "line": 131, "column": 5, "nodeType": "2060", "endLine": 131, "endColumn": 155, "suggestions": "2896"}, {"ruleId": "1954", "severity": 1, "message": "2260", "line": 201, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 201, "endColumn": 21}, {"ruleId": "2058", "severity": 1, "message": "2897", "line": 217, "column": 8, "nodeType": "2060", "endLine": 217, "endColumn": 24, "suggestions": "2898"}, {"ruleId": "1954", "severity": 1, "message": "2535", "line": 1, "column": 27, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 41}, {"ruleId": "1954", "severity": 1, "message": "2899", "line": 22, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 23}, {"ruleId": "1954", "severity": 1, "message": "2900", "line": 31, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 31, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2901", "line": 40, "column": 7, "nodeType": "1956", "messageId": "1957", "endLine": 40, "endColumn": 25}, {"ruleId": "1954", "severity": 1, "message": "2902", "line": 5, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 5, "endColumn": 24}, {"ruleId": "2058", "severity": 1, "message": "2903", "line": 121, "column": 6, "nodeType": "2060", "endLine": 121, "endColumn": 26, "suggestions": "2904"}, {"ruleId": "1954", "severity": 1, "message": "2905", "line": 39, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 39, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2266", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 27}, {"ruleId": "1954", "severity": 1, "message": "2524", "line": 1, "column": 28, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 36}, {"ruleId": "1954", "severity": 1, "message": "1964", "line": 2, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 2, "endColumn": 22}, {"ruleId": "1954", "severity": 1, "message": "2270", "line": 4, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 4, "endColumn": 24}, {"ruleId": "1954", "severity": 1, "message": "2739", "line": 6, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 6, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2740", "line": 7, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 7, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "1986", "line": 8, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2906", "line": 9, "column": 2, "nodeType": "1956", "messageId": "1957", "endLine": 9, "endColumn": 19}, {"ruleId": "1954", "severity": 1, "message": "2265", "line": 11, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 11, "endColumn": 8}, {"ruleId": "1954", "severity": 1, "message": "2348", "line": 13, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 13, "endColumn": 14}, {"ruleId": "1954", "severity": 1, "message": "2226", "line": 15, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 15, "endColumn": 16}, {"ruleId": "1954", "severity": 1, "message": "2227", "line": 16, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 16, "endColumn": 18}, {"ruleId": "1954", "severity": 1, "message": "2350", "line": 18, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 18, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2351", "line": 19, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 19, "endColumn": 11}, {"ruleId": "1954", "severity": 1, "message": "2352", "line": 20, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2353", "line": 21, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 21, "endColumn": 15}, {"ruleId": "1954", "severity": 1, "message": "2354", "line": 22, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 22, "endColumn": 12}, {"ruleId": "1954", "severity": 1, "message": "2355", "line": 23, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 23, "endColumn": 10}, {"ruleId": "1954", "severity": 1, "message": "2356", "line": 24, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 24, "endColumn": 9}, {"ruleId": "1954", "severity": 1, "message": "2907", "line": 25, "column": 5, "nodeType": "1956", "messageId": "1957", "endLine": 25, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2473", "line": 1, "column": 17, "nodeType": "1956", "messageId": "1957", "endLine": 1, "endColumn": 26}, {"ruleId": "1954", "severity": 1, "message": "2868", "line": 61, "column": 9, "nodeType": "1956", "messageId": "1957", "endLine": 61, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2869", "line": 63, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 63, "endColumn": 20}, {"ruleId": "1954", "severity": 1, "message": "2353", "line": 8, "column": 3, "nodeType": "1956", "messageId": "1957", "endLine": 8, "endColumn": 13}, {"ruleId": "1954", "severity": 1, "message": "2908", "line": 12, "column": 8, "nodeType": "1956", "messageId": "1957", "endLine": 12, "endColumn": 21}, {"ruleId": "1954", "severity": 1, "message": "2909", "line": 20, "column": 10, "nodeType": "1956", "messageId": "1957", "endLine": 20, "endColumn": 30}, {"ruleId": "1954", "severity": 1, "message": "2542", "line": 75, "column": 29, "nodeType": "1956", "messageId": "1957", "endLine": 75, "endColumn": 45}, {"ruleId": "1954", "severity": 1, "message": "2910", "line": 77, "column": 11, "nodeType": "1956", "messageId": "1957", "endLine": 77, "endColumn": 22}, {"ruleId": "2058", "severity": 1, "message": "2911", "line": 123, "column": 6, "nodeType": "2060", "endLine": 123, "endColumn": 22, "suggestions": "2912"}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'LoginUserInfo' is defined but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'GetOrganizationThemes' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["2913"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2914"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2915"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2916"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2917"], "React Hook useEffect has a missing dependency: 'setIsAIGuidePersisted'. Either include it or remove the dependency array.", ["2918"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2919"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'resetALTKeywordForNewTooltip', 'setElementSelected', 'setIsALTKeywordEnabled', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2920"], "'handleElementSelectionToggle' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2921"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2922"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userType'. Either include it or remove the dependency array.", ["2923"], "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2924"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2925"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2926"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2927"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2928"], "React Hook useEffect has a missing dependency: 'fetchThemeDetails'. Either include it or remove the dependency array.", ["2929"], "'buttonStyles' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyThemeToAllButtons', 'autoPosition', 'backgroundColor', 'setAnnBorderSize', 'setAnnPadding', 'setBackgroundColor', 'setBannerCanvasSetting', 'setBorderColor', 'setBorderRadius', 'setCanvasSetting', 'setWidth', 'tooltipPosition', 'tooltipWidth', 'tooltipXaxis', 'tooltipYaxis', 'updateCanvasInTooltip', and 'updateChecklistCanvas'. Either include them or remove the dependency array.", ["2930"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCreateWithAI', 'setCurrentGuideId', 'setIsAIGuidePersisted', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2931"], "React Hook useEffect has an unnecessary dependency: 'updatedGuideData'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData' aren't valid dependencies because mutating them doesn't re-render the component.", ["2932"], "'getAccountIdForUpdate' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2933", "2934"], "'selectedStepTitle' is assigned a value but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", ["2935"], "'signIn' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "'isAnnouncement' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "no-self-assign", "'state.overlayEnabled' is assigned to itself.", "selfAssignment", "Duplicate key 'toolTipGuideMetaData'.", "'changeType' is assigned a value but never used.", "'isTourBanner' is assigned a value but never used.", "'announcementGuideStep' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2936"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2937"], "'buttonShape' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyThemeToAllButtons', 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', and 'defaultButtonColors.color'. Either include them or remove the dependency array.", ["2938"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2939"], "'Box' is defined but never used.", "'useContext' is defined but never used.", "'Typography' is defined but never used.", "'AuthProvider' is defined but never used.", "'useAuth' is defined but never used.", "'AccountContext' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'setThemeValues' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2940"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2941"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2942"], "'handleEnableAI' is assigned a value but never used.", "'useDrawerStore' is defined but never used.", "'constants' is defined but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", ["2943", "2944"], "Unnecessary escape character: \\..", ["2945", "2946"], ["2947", "2948"], ["2949", "2950"], ["2951", "2952"], ["2953", "2954"], ["2955", "2956"], ["2957", "2958"], "'response' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "VariableDeclarator", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2959"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2960"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2961"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2962"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2963"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2964"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2965"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2966"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2967"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2968"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2969"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2970"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2971"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2972"], ["2973"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'count' is assigned a value but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyCustomCursor', 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2974"], "React Hook useEffect has a missing dependency: 'applyCustomCursor'. Either include it or remove the dependency array.", ["2975"], "'normalizePx' is assigned a value but never used.", "'DotsStepper' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2976"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setActiveMenu' is assigned a value but never used.", "'setSearchText' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2977"], ["2978"], "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "'setIsThemeChanges' is assigned a value but never used.", "'fontFamily' is assigned a value but never used.", "'fontSize' is assigned a value but never used.", "'fontColor' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'selectedTheme?.ThemeStyles?.Typography?.fontColor', 'selectedTheme?.ThemeStyles?.Typography?.fontFamily', and 'selectedTheme?.ThemeStyles?.Typography?.fontSize'. Either include them or remove the dependency array.", ["2979"], "React Hook useMemo has missing dependencies: 'fontColor', 'fontFamily', 'fontSize', and 'handlePaste'. Either include them or remove the dependency array.", ["2980"], "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2981"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2982"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2983"], ["2984"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2985"], "'toggleItemCompletion' is assigned a value but never used.", "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'accountId' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["2986"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2987"], "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2988"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2989"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", ["2990"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'canvas' is assigned a value but never used.", "'OverlaySettingsProps' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2991"], "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'selectedTheme' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2992"], "React Hook useEffect has missing dependencies: 'applyThemeToAllButtons', 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'settingAnchorEl.buttonId', and 'settingAnchorEl.containerId'. Either include them or remove the dependency array.", ["2993"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2994"], "'handlePositionClick' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2995"], "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2996"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2997"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2998"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2999"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["3000"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["3001"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["3002"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["3003"], ["3004"], ["3005"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["3006"], "'guideStatus' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["3007"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["3008"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["3009"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["3010"], "'handleColorChange' is assigned a value but never used.", "'FolderIcon' is defined but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["3011"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "'handleTooltipRTEValue' is assigned a value but never used.", "'anchorPosition' is assigned a value but never used.", "'setAnchorPosition' is assigned a value but never used.", "'preserveCaretPosition' is assigned a value but never used.", "'restoreCaretPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'boxRef' and 'textvaluess'. Either include them or remove the dependency array. Mutable values like 'boxRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["3012"], "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["3013"], "React Hook useEffect has a missing dependency: 'applyThemeButtonInTooltip'. Either include it or remove the dependency array.", ["3014"], "'getSavedLanguage' is assigned a value but never used.", "'saveLanguage' is assigned a value but never used.", "'clearSavedLanguage' is assigned a value but never used.", "'getAvailableLanguages' is defined but never used.", "React Hook useEffect has a missing dependency: 'userInfoObj'. Either include it or remove the dependency array.", ["3015"], "'orgId' is assigned a value but never used.", "'DialogContentText' is defined but never used.", "'TextareaAutosize' is defined but never used.", "'PlayArrowIcon' is defined but never used.", "'getThemesByAccountId' is defined but never used.", "'userDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId', 'organizationId', 'setIsThemeChanges', and 'setSelectedTheme'. Either include them or remove the dependency array.", ["3016"], {"desc": "3017", "fix": "3018"}, {"desc": "3019", "fix": "3020"}, {"desc": "3021", "fix": "3022"}, {"desc": "3023", "fix": "3024"}, {"desc": "3025", "fix": "3026"}, {"desc": "3027", "fix": "3028"}, {"desc": "3029", "fix": "3030"}, {"desc": "3031", "fix": "3032"}, {"desc": "3033", "fix": "3034"}, {"desc": "3035", "fix": "3036"}, {"desc": "3037", "fix": "3038"}, {"desc": "3039", "fix": "3040"}, {"desc": "3041", "fix": "3042"}, {"desc": "3043", "fix": "3044"}, {"desc": "3045", "fix": "3046"}, {"desc": "3047", "fix": "3048"}, {"desc": "3049", "fix": "3050"}, {"desc": "3051", "fix": "3052"}, {"desc": "3053", "fix": "3054"}, {"desc": "3055", "fix": "3056"}, {"messageId": "3057", "fix": "3058", "desc": "3059"}, {"messageId": "3060", "fix": "3061", "desc": "3062"}, {"desc": "3063", "fix": "3064"}, {"desc": "3065", "fix": "3066"}, {"desc": "3067", "fix": "3068"}, {"desc": "3069", "fix": "3070"}, {"desc": "3071", "fix": "3072"}, {"desc": "3073", "fix": "3074"}, {"desc": "3075", "fix": "3076"}, {"desc": "3077", "fix": "3078"}, {"messageId": "3057", "fix": "3079", "desc": "3059"}, {"messageId": "3060", "fix": "3080", "desc": "3062"}, {"messageId": "3057", "fix": "3081", "desc": "3059"}, {"messageId": "3060", "fix": "3082", "desc": "3062"}, {"messageId": "3057", "fix": "3083", "desc": "3059"}, {"messageId": "3060", "fix": "3084", "desc": "3062"}, {"messageId": "3057", "fix": "3085", "desc": "3059"}, {"messageId": "3060", "fix": "3086", "desc": "3062"}, {"messageId": "3057", "fix": "3087", "desc": "3059"}, {"messageId": "3060", "fix": "3088", "desc": "3062"}, {"messageId": "3057", "fix": "3089", "desc": "3059"}, {"messageId": "3060", "fix": "3090", "desc": "3062"}, {"messageId": "3057", "fix": "3091", "desc": "3059"}, {"messageId": "3060", "fix": "3092", "desc": "3062"}, {"messageId": "3057", "fix": "3093", "desc": "3059"}, {"messageId": "3060", "fix": "3094", "desc": "3062"}, {"desc": "3095", "fix": "3096"}, {"desc": "3097", "fix": "3098"}, {"desc": "3099", "fix": "3100"}, {"desc": "3101", "fix": "3102"}, {"desc": "3103", "fix": "3104"}, {"desc": "3105", "fix": "3106"}, {"desc": "3107", "fix": "3108"}, {"desc": "3109", "fix": "3110"}, {"messageId": "3111", "data": "3112", "fix": "3113", "desc": "3114"}, {"desc": "3115", "fix": "3116"}, {"desc": "3117", "fix": "3118"}, {"desc": "3119", "fix": "3120"}, {"desc": "3121", "fix": "3122"}, {"desc": "3123", "fix": "3124"}, {"desc": "3125", "fix": "3126"}, {"desc": "3127", "fix": "3128"}, {"desc": "3129", "fix": "3130"}, {"desc": "3131", "fix": "3132"}, {"desc": "3133", "fix": "3134"}, {"desc": "3135", "fix": "3136"}, {"desc": "3137", "fix": "3138"}, {"desc": "3139", "fix": "3140"}, {"desc": "3141", "fix": "3142"}, {"desc": "3143", "fix": "3144"}, {"desc": "3145", "fix": "3146"}, {"desc": "3095", "fix": "3147"}, {"desc": "3148", "fix": "3149"}, {"desc": "3150", "fix": "3151"}, {"desc": "3152", "fix": "3153"}, {"desc": "3154", "fix": "3155"}, {"desc": "3156", "fix": "3157"}, {"desc": "3148", "fix": "3158"}, {"desc": "3159", "fix": "3160"}, {"desc": "3161", "fix": "3162"}, {"desc": "3163", "fix": "3164"}, {"desc": "3165", "fix": "3166"}, {"desc": "3167", "fix": "3168"}, {"desc": "3169", "fix": "3170"}, {"desc": "3171", "fix": "3172"}, {"desc": "3173", "fix": "3174"}, {"desc": "3175", "fix": "3176"}, {"desc": "3177", "fix": "3178"}, {"desc": "3179", "fix": "3180"}, {"desc": "3181", "fix": "3182"}, {"desc": "3183", "fix": "3184"}, {"desc": "3183", "fix": "3185"}, {"desc": "3186", "fix": "3187"}, {"desc": "3188", "fix": "3189"}, {"desc": "3190", "fix": "3191"}, {"desc": "3192", "fix": "3193"}, {"desc": "3190", "fix": "3194"}, {"desc": "3195", "fix": "3196"}, {"desc": "3197", "fix": "3198"}, {"desc": "3199", "fix": "3200"}, {"desc": "3201", "fix": "3202"}, {"desc": "3203", "fix": "3204"}, {"desc": "3205", "fix": "3206"}, {"desc": "3207", "fix": "3208"}, "Update the dependencies array to be: []", {"range": "3209", "text": "3210"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "3211", "text": "3212"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3213", "text": "3214"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3215", "text": "3216"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3217", "text": "3218"}, "Update the dependencies array to be: [initialState, setIsAIGuidePersisted]", {"range": "3219", "text": "3220"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3221", "text": "3222"}, "Update the dependencies array to be: [handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", {"range": "3223", "text": "3224"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3225", "text": "3226"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3227", "text": "3228"}, "Update the dependencies array to be: [isLoggedIn, organizationId, userType]", {"range": "3229", "text": "3230"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3231", "text": "3232"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3233", "text": "3234"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3235", "text": "3236"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3237", "text": "3238"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3239", "text": "3240"}, "Update the dependencies array to be: [accountId, fetchThemeDetails]", {"range": "3241", "text": "3242"}, "Update the dependencies array to be: [applyThemeToAllButtons, autoPosition, backgroundColor, selectedTheme, setAnnBorderSize, setAnnPadding, setBackgroundColor, setBannerCanvasSetting, setBorderColor, setBorderRadius, setCanvasSetting, setWidth, steps, tooltipPosition, tooltipWidth, tooltipXaxis, tooltipYaxis, updateCanvasInTooltip, updateChecklistCanvas]", {"range": "3243", "text": "3244"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3245", "text": "3246"}, "Update the dependencies array to be: [pendingWebTourModal, isTourPopupOpen, tourModalSource]", {"range": "3247", "text": "3248"}, "removeEscape", {"range": "3249", "text": "3250"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3251", "text": "3252"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [loggedOut]", {"range": "3253", "text": "3254"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3255", "text": "3256"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, selectedTheme, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3257", "text": "3258"}, "Update the dependencies array to be: [applyThemeToAllButtons, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, selectedTheme]", {"range": "3259", "text": "3260"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3261", "text": "3262"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3263", "text": "3264"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3265", "text": "3266"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3267", "text": "3268"}, {"range": "3269", "text": "3250"}, {"range": "3270", "text": "3252"}, {"range": "3271", "text": "3250"}, {"range": "3272", "text": "3252"}, {"range": "3273", "text": "3250"}, {"range": "3274", "text": "3252"}, {"range": "3275", "text": "3250"}, {"range": "3276", "text": "3252"}, {"range": "3277", "text": "3250"}, {"range": "3278", "text": "3252"}, {"range": "3279", "text": "3250"}, {"range": "3280", "text": "3252"}, {"range": "3281", "text": "3250"}, {"range": "3282", "text": "3252"}, {"range": "3283", "text": "3250"}, {"range": "3284", "text": "3252"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3285", "text": "3286"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3287", "text": "3288"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3289", "text": "3290"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3291", "text": "3292"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3293", "text": "3294"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3295", "text": "3296"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3297", "text": "3298"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3299", "text": "3300"}, "suggestString", {"type": "3301"}, {"range": "3302", "text": "3303"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3304", "text": "3305"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3306", "text": "3307"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3308", "text": "3309"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3310", "text": "3311"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3312", "text": "3313"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3314", "text": "3315"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", {"range": "3316", "text": "3317"}, "Update the dependencies array to be: [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", {"range": "3318", "text": "3319"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3320", "text": "3321"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3322", "text": "3323"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3324", "text": "3325"}, "Update the dependencies array to be: [editingRTEId, selectedTheme?.ThemeStyles?.Typography?.fontColor, selectedTheme?.ThemeStyles?.Typography?.fontFamily, selectedTheme?.ThemeStyles?.Typography?.fontSize]", {"range": "3326", "text": "3327"}, "Update the dependencies array to be: [fontColor, fontFamily, fontSize, handlePaste, isRtlDirection]", {"range": "3328", "text": "3329"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3330", "text": "3331"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3332", "text": "3333"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3334", "text": "3335"}, {"range": "3336", "text": "3286"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3337", "text": "3338"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3339", "text": "3340"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3341", "text": "3342"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3343", "text": "3344"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3345", "text": "3346"}, {"range": "3347", "text": "3338"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3348", "text": "3349"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3350", "text": "3351"}, "Update the dependencies array to be: [applyThemeToAllButtons, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, selectedTheme, settingAnchorEl.buttonId, settingAnchorEl.containerId]", {"range": "3352", "text": "3353"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3354", "text": "3355"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3356", "text": "3357"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3358", "text": "3359"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3360", "text": "3361"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3362", "text": "3363"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3364", "text": "3365"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3366", "text": "3367"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3368", "text": "3369"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3370", "text": "3371"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3372", "text": "3373"}, {"range": "3374", "text": "3373"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3375", "text": "3376"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3377", "text": "3378"}, "Update the dependencies array to be: [fetchData]", {"range": "3379", "text": "3380"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3381", "text": "3382"}, {"range": "3383", "text": "3380"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3384", "text": "3385"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3386", "text": "3387"}, "Update the dependencies array to be: [rteBoxValue, boxRef, textvaluess]", {"range": "3388", "text": "3389"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3390", "text": "3391"}, "Update the dependencies array to be: [applyThemeButtonInTooltip, selectedTheme]", {"range": "3392", "text": "3393"}, "Update the dependencies array to be: [orgId, accessToken, userInfoObj]", {"range": "3394", "text": "3395"}, "Update the dependencies array to be: [accountId, isThemeChanges, organizationId, setIsThemeChanges, setSelectedTheme]", {"range": "3396", "text": "3397"}, [18482, 18504], "[]", [27015, 27056], "[fetchGuideDetails, hotspot, hotspotClicked]", [27538, 27551], "[designPopup, setDesignPopup]", [30275, 30422], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30900, 31260], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [31628, 31642], "[initialState, setIsAIGuidePersisted]", [36070, 36104], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [71906, 71970], "[handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", [88142, 88175], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [102870, 102884], "[createWithAI, setIsUnSavedChanges, stepCreation]", [126293, 126321], "[isLoggedIn, organizationId, userType]", [132291, 132443], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [162086, 162143], "[currentGuide?.GuideStep, currentStep]", [167400, 167417], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [173349, 173382], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [174092, 174193], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [176624, 176635], "[accountId, fetchThemeDetails]", [183939, 183961], "[applyThemeToAllButtons, autoPosition, backgroundColor, selectedTheme, setAnnBorderSize, setAnnPadding, setBackgroundColor, setBannerCanvasSetting, setBorderColor, setBorderRadius, setCanvasSetting, setWidth, steps, tooltipPosition, tooltipWidth, tooltipXaxis, tooltipYaxis, updateCanvasInTooltip, updateChecklistCanvas]", [185753, 185765], "[isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [186185, 186258], "[pendingWebTourModal, isTourPopupOpen, tourModalSource]", [208775, 208776], "", [208775, 208775], "\\", [4501, 4503], "[loggedOut]", [16189, 16244], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17641, 17711], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, selectedTheme, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [18362, 18378], "[applyThemeToAllButtons, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, selectedTheme]", [18836, 18838], "[selectedActions.value, targetURL]", [2467, 2469], "[isExtensionClosed, setIsExtensionClosed]", [3134, 3157], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3501, 3544], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [22521, 22522], [22521, 22521], [22525, 22526], [22525, 22525], [22536, 22537], [22536, 22536], [22540, 22541], [22540, 22540], [22569, 22570], [22569, 22569], [22573, 22574], [22573, 22573], [22584, 22585], [22584, 22584], [22588, 22589], [22588, 22588], [5960, 5993], "[checkpointslistData]", [5310, 5350], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10234, 10269], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [25153, 25248], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", [25832, 25910], "[isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [9051, 9096], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [9402, 9415], "[fetchAnnouncements, searchQuery]", [5732, 5746], "[editing<PERSON><PERSON>d, selectedTheme?.ThemeStyles?.Typography?.fontColor, selectedTheme?.ThemeStyles?.Typography?.fontFamily, selectedTheme?.ThemeStyles?.Typography?.fontSize]", [15176, 15206], "[fontColor, fontFamily, fontSize, handlePaste, isRtlDirection]", [2695, 2697], "[setButtonProperty]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4604, 4606], "[accountId, openSnackbar]", [17421, 17423], "[setElementSelected]", [4877, 4879], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9655, 9657], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [6878, 6903], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [4715, 4770], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [5401, 5416], "[applyThemeToAllButtons, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, selectedTheme, settingAnchorEl.buttonId, settingAnchorEl.containerId]", [7195, 7250], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [9769, 9782], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26438, 26454], "[handleFocus, isRtlDirection]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [34487, 34516], "[currentStepData, currentStepIndex, handleNext]", [34993, 35022], "[currentStepData, currentUrl, updateTargetAndPosition]", [35434, 35463], [35522, 35557], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7948, 7995], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [3834, 3863], "[rteBoxV<PERSON>ue, boxRef, textvaluess]", [4349, 4499], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", [7114, 7130], "[applyThemeButtonInTooltip, selectedTheme]", [4596, 4616], "[orgId, accessToken, userInfoObj]", [3864, 3880], "[accountId, isThemeChanges, organizationId, setIsThemeChanges, setSelectedTheme]"]