@import url('https://fonts.googleapis.com/css2?family=Syncopate:wght@700&display=swap');
@import '../components/guideDesign/Canvas.module.scss';

:root {
	--font-family: Gotham Pro, Proxima Nova, arial, serif;
    --primarycolor: #5F9EA0;
    --ext-background: #F6EEEE;
    --border-color: #ccc;
    --white-color: #fff;
    --back-light-color:#EAE2E2;
    --primaryColor: #FFFFFF;
    --secondaryColor: #FCF3F0;
    --accentColor: #5F9EA0;
    --fontFamily: Poppins;
    --shape: 4px;

    /* Dynamic color variants - these will be set by JavaScript */
    /* Primary Color Variants */
    --primaryColor-10: rgba(255, 255, 255, 0.1);
    --primaryColor-20: rgba(255, 255, 255, 0.2);
    --primaryColor-30: rgba(255, 255, 255, 0.3);
    --primaryColor-40: rgba(255, 255, 255, 0.4);
    --primaryColor-50: rgba(255, 255, 255, 0.5);

    /* Secondary Color Variants */
    --secondaryColor-10: rgba(252, 243, 240, 0.1);
    --secondaryColor-20: rgba(252, 243, 240, 0.2);
    --secondaryColor-30: rgba(252, 243, 240, 0.3);
    --secondaryColor-40: rgba(252, 243, 240, 0.4);
    --secondaryColor-50: rgba(252, 243, 240, 0.5);

    /* Accent Color Variants */
    --accentColor-10: rgba(95, 158, 160, 0.1);
    --accentColor-20: rgba(95, 158, 160, 0.2);
    --accentColor-30: rgba(95, 158, 160, 0.3);
    --accentColor-40: rgba(95, 158, 160, 0.4);
    --accentColor-50: rgba(95, 158, 160, 0.5);
}
*:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab) {
	font-family: var(--font-family) !important;
}
body {
	margin: 0;
	font-family: var(--font-family) !important;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

@font-face {
	font-family: "Gotham Pro";
	font-style: normal;
	src: local("Gotham Pro"), local("Gotham Pro"), url("../assets/fonts/GothamPro.woff2") format("woff2"),
		url("../assets/fonts/GothamPro.woff") format("woff");
}

@font-face {
	font-family: "Proxima Nova";
	font-style: normal;
	src: local("Proxima Nova"), local("ProximaNova-Regular"),
		url("../assets/fonts/ProximaNova-Regular.woff2") format("woff2"),
		url("../assets/fonts/ProximaNova-Regular.woff") format("woff");
}

@font-face {
	font-family: "qadapt-icons";
	src: url("../assets/fonts/qadapt-icons.eot?qmcsfb");
	src: url("../assets/fonts/qadapt-icons.eot?qmcsfb#iefix") format("embedded-opentype"),
		url("../assets/fonts/qadapt-icons.ttf?qmcsfb") format("truetype"),
		url("../assets/fonts/qadapt-icons.woff?qmcsfb") format("woff"),
		url("../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons") format("svg");
	font-weight: normal;
	font-style: normal;
	font-display: block;
}
.leftDrawer {
    position: fixed;
    left: 0;
    top: 0;
    width: 270px !important; 
    height: 100%;
    background-color: var(--ext-background);
    border-right: 1px solid var(--primarycolor);
    transition: width 0.3s ease, background-color 0.3s ease !important;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 20px;
    z-index: 999;
    overflow: auto;
    &.collapsed {
      width: 35px;
      background-color: var(--primarycolor) !important;
      padding: 20px 5px;
    }
  }
  
 
  .qadpt-drawerHeader {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    position: relative;
    .qadpt-toggleIcon {
      position: absolute;
      left: 10px;
      cursor: pointer;
    }
  
    .qadpt-leftdrawer.collapsed .qadpt-toggleIcon {
      left: 3px;
      margin-top: 40px;
    }
  
    .qadpt-drawerTitle {
      font-family: "Syncopate", sans-serif !important;
      font-size: 14px;
      font-weight: 700;
      color: var(--primarycolor);
    }
  
    .qadpt-threeDotMenu {
      position: absolute;
      right: -30px;
      cursor: pointer;
      width: 20px;
      height: 20px;
      margin-right: 20px;
    }
  }
  
 

 
  /* #newInteractionBtn:hover {
    background-color: #34A080;
  } */
  // .qadpt-drawerContent {
    // width: 100%;
    // background-color: var(--ext-background);
    // margin-top: 20px;
    // .qadpt-welcome-message {
    //   font-size: 16px;
    //   font-weight: 600;
    //   text-align: left;
    //   padding: 13px;
    // }
    // .qadpt-login-form {
      // margin-top: 20px;
      // .qadpt-form-label {
      //   font-size: 14px;
      //   margin-top: 10px;
      //   text-align: left;
      // }
      // .qadpt-input-field {
      //   font-size: 16px;
      //   font-weight: 400;
      //   padding: 12px;
      //   border: 1px solid var(--border-color);
      //   border-radius: 6px;
      //   box-shadow: none;
      //   height: 46px;
      //   background-color: var(--white-color);
      //   margin-top: 10px;
      // }
      // .qadpt-invalidcreds {
      //   font-size: 14px;
      // }
    //   .qadpt-forgotpwd {
    //     color: var(--primarycolor) !important;
    //     cursor: pointer;
    //     font-size: 16px;
    //     font-weight: 400;
    //     line-height: 24px;
    //     margin-top: 10px;
    //     text-align: left;
    //   }
    // }
    // .qadpt-subhead {
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   position: relative;
    //   .qadpt-backbtn {
    //     position: absolute;
    //     left: 10px;
    //     cursor: pointer;
    //   }
    //   .qadpt-subhead-title {
    //     font-size: 18px;
    //     font-weight: 600;
    //   }
    // }
    // .qadpt-divider {
    //   margin-top: 10px;
    // }
    // .qadpt-items {
    //   display: grid;
    //   gap: 10px;
    //   margin-top: 20px;
    //   margin-left: -4px;
    //   .qadpt-subitem-img {
    //     width: 130px;
    //     height: 80px;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     border-radius: 8px;
    //     margin-bottom: 10px;
    //     cursor: pointer;
  
    //     &.selected {
    //       background: linear-gradient(
    //         270deg,
    //         #ededed 0%,
    //         rgba(95, 158, 160, 0.5) 100%
    //       );
    //     }
  
    //     &:not(.selected) {
    //       background: #eae2e2;
    //     }
    //   }
    //   .qadpt-item-label {
    //     font-size: 14px;
    //     font-weight: 600;
    //     text-align: left;
    //   }
    // }
    // .qadpt-guide-form {
    //   margin-top: 20px;
    //   .qadpt-guide-label {
    //     font-size: 14px;
    //     font-weight: 600;
    //     color: #444444;
    //     margin-bottom: 5px;
    //     text-align: left;
    //   }
    //   .qadpt-guide-input {
    //     // width: 270px;
    //     height: 45px;
    //     padding: 12px;
    //     gap: 10px;
    //     border-radius: 4px;
    //     background-color: #eae2e2;
    //     opacity: 1;
    //     margin-bottom: 15px;
    //   }
    // }
    // .qadpt-drawerFooter {
    //   bottom: 50px;
    //   position: absolute;
    //   width: 87%;
    // }
    // .qadpt-crtfrm-scratch {
    //   // width: 270px;
    //   height: 100px;
    //   background-color: #eae2e2;
    //   display: flex;
    //   flex-direction: column;
    //   justify-content: center;
    //   align-items: center;
    //   border-radius: 4px;
    //   margin-top: 10px;
    //   gap: 10px;
    // }
  // }
  .qadpt-btn {
    background-color: var(--primarycolor) !important;
    color: var(--white-color) !important;
    border-radius: 10px !important;
    width: 100% !important;
    margin-top: 10px !important;
    text-transform: none !important;
    font-size: 16px !important;
    border: 0 !important;
    line-height: 24px !important;
    padding: 10px !important;
    &.disabled {
      background-color: #a0a0a0 !important;
    }
  }
  .primary-color {
    background-color: var(--primaryColor);
    color: var(--font-color);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 6px;
    margin-bottom: 20px;
  }
  
  .secondary-color {
    background-color: var(--secondaryColor, #e72a2a);
    color: var(--font-color);
    font-size: 14px;
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 8px;
  }
  
  .fontstyle{
	font-family: var(--fontFamily), sans-serif !important;
	font-style: normal !important;
	font-weight: 400 !important;
	font-size: 16px !important;
	line-height: 1.5 !important;
  }
  .accent-color {
    background-color: var(--accentColor);
    color: var(--font-color);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 6px;
    margin-bottom: 20px;
  }
  .qadpt-ext-banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    background-color: var(--ext-background);
    border-bottom: 1px solid var(--primarycolor);
    height: 55px;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999999;
    .qadpt-banner-button {
      border: 1px solid var(--primarycolor);
      padding: 5px;
      border-radius: 10px;
      color: var(--primarycolor);
      gap: 5px;
      svg {
        margin-top: 5px;
      }
      &.qadpt-icon {
        padding: 5px 12px !important;
      }
    }
    .qadpt-left-banner,
    .qadpt-center-banner,
    .qadpt-right-banner {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .qadpt-right-banner {
      margin-right: 37px;
    }
  }
  .qadpt-threedot-popup {
    // z-index: 99999999 !important;
  
    .MuiPopover-paper {
      width: 190px;
    height: 120px;
      padding: 15px;
      margin-top: 40px;
      margin-left: 100px;
      z-index: 9999;
    }
  }
  
  .qadpt-popup-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 10px;
  
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .qadpt-popup-icon {
    margin-right: 10px;
  }
  
  .qadpt-popup-text {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    text-align: left;
  }
  
  /* Ensure dropdown list has higher z-index */
  .MuiPopover-root .MuiSelect-root {
    z-index: 1000000 !important;
  }
  
  
